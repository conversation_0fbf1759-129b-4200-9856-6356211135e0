{"key": "group_68a339bc60d0b", "title": "Gift call to actions fields", "fields": [{"key": "field_68a339bc51139", "label": "Items", "name": "items", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_68a33a595113a", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "allow_in_bindings": 0, "parent_repeater": "field_68a339bc51139"}, {"key": "field_68a33a958df22", "label": "Class", "name": "class", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"outline": "Outline", "secondary": "<PERSON><PERSON><PERSON>"}, "default_value": "default", "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 0, "ui": 0, "ajax": 0, "placeholder": "", "parent_repeater": "field_68a339bc51139"}]}, {"key": "field_68a33a6d5113b", "label": "Text", "name": "text", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": "", "placeholder": "", "new_lines": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/gift-ctas"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1755528323}