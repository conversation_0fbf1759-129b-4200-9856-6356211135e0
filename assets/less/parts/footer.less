// out: false
@import '../vw_values.less';
@import '../constants.less';

.footerForm {
  padding: @vw100 0;
  background: @primaryColorDark;
  .formWrapper {
    padding: @vw100 0;
    .mediumTitle {
      padding: 0 @vw100 * 2;
      line-height: 1.2;
      color: @almostWhite;
      text-align: center;
      margin-bottom: @vw80;
    }
    .wpcf7-form {
      width: 100%;
      .wpcf7-form-control-wrap {
        width: 100%;
      }
      input.wpcf7-form-control, .wpcf7-form textarea.wpcf7-form-control {
        padding-left: 0;
        border: none;
        border-bottom: 1px solid @primaryColor;
        .rounded(0) !important;
      }
    }
  }
}

.footer {
    padding: @vw100 + @vw20 0 @vw80 0;
    color: @almostWhite;
    position: relative;
    overflow: hidden;
    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(@secondaryColor, 1);
    }
    .backgroundWrapper { 
      width: 60vw;
      position: absolute;
      height: 60vw;
      .transform(translateY(-50%));
      top: 50%;
      opacity: 1;
      left: 0;
      .background {
        opacity: 1;
        position: absolute;
        animation: moveBackground 10s infinite ease-in-out alternate;
        top: 0;
        left: 0;
        width: 100%;
        .rounded(50%);
        height: 100%; 
        background: rgba(@primaryColor, .2);
        -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
        mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
      }
    }
    .bottomFooter {
        color: rgba(@hardWhite, .7);
        .cols {
          display: flex;
          align-items: start;
          flex-direction: row;
          flex-wrap: wrap;
          &:not(:last-child) {
            margin-bottom: @vw40;
          }
        }
        .col {
          display: inline-block;
          vertical-align: top;
          width: 50%;
          &.logoCol {
            padding-right: @vw100 * 2.4;
          }
          .innerCol {
            display: inline-block;
            vertical-align: top;
            width: 49%;
            &:first-child {
              padding-right: @vw40;
            }
            &:last-child {
              display: inline-flex;
              flex-direction: row;
              gap: @vw40;
            }
            .innerItem {
              &:not(:last-child) {
                margin-bottom: @vw50;
              }
            }
            p {
              color: rgba(@hardWhite, .7);
            }
          }
        }
        ul {
          margin-top: @vw16;
          list-style: none;
          li {
            display: table;
            a {
              color: rgba(@hardWhite, .7);
              cursor: pointer;
              text-decoration: none;
              .transitionMore(color, .3s);
              &:hover {
                color: rgba(@primaryColor, .7);
              }
            }
          }
        }
    }
    .logo {
      width: 100%;
      display: inline-block;
      vertical-align: middle;
      cursor: pointer;
      height: @vw90;
      .transitionMore(opacity, .3s);
      &:hover {
        opacity: .4;
      }
      * {
        cursor: pointer;
      }
      img {
        object-fit: contain;
        width: auto;
        height: 100%;
      }
    }
    .socials {
      margin: @vw30 0;
      .socialLink {
        margin-right: @vw10;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .address, .signature {
      font-size: 12px !important;
      line-height: 1.4;
    }
    .signature {
      display: inline-block;
      color: @primaryColor;
      cursor: pointer;
      text-decoration: none;
      margin-top: @vw24;
      .transitionMore(opacity, .3s);
      * {
        cursor: pointer;
      }
      &:hover {
        opacity: .4;
      }
    }
    .innerMenu {
      display: inline-block;
      vertical-align: top;
      list-style: none;
      li {
        display: table;
      }
      h3, li {
        &:not(:last-child) {
            margin-bottom: @vw10;
        }
      }
      a {
          display: inline-block;
          vertical-align: middle;
          cursor: pointer;
          font-weight: 400;
          color: @hardWhite;
          text-decoration: none;
          transition: color .3s, transform .3s;
          &:hover {
              color: @primaryColor;
          }
      }
  }
}

@media all and (max-width: 1080px) {
    .footer {
      padding: @vw100-1080 0 @vw22-1080 0;
      .topFooter {
        .cols {
          margin-left: -@vw22-1080;
          width: calc(100% ~"+" @vw44-1080);
          .col {
            margin: 0 @vw22-1080;
            width: calc(25% ~"-" @vw50-1080);
          }
        }
      }
      .bottomFooter {
          .cols {
            &:not(:last-child) {
              margin-bottom: @vw40-1080;
            }
            &:first-child {
              .col {
                &:nth-child(2) {
                  width: 60%;
                }
              }
            }
          }
          .col {
            width: 50%;
            &.logoCol {
              width: 40%;
              padding-right: @vw100-1080;
            }
          }
          ul {
            li {
              &:not(:last-child) {
                margin-right: @vw22-1080;
              }
            }
          }
      }
      .contactCol {
        &:not(:last-child) {
          margin-right: @vw50-1080;
        }
        .link {
          &:not(:last-child) {
            margin-bottom: @vw10-1080;
          }
        }
        .smallTitle {
          margin-bottom: @vw12-1080;
        }
      }
      .divider {
        margin: @vw70-1080 0;
      }
      .innerMenu {
        h3, li {
          &:not(:last-child) {
              margin-bottom: @vw16-1080;
          }
        }
        a {
            padding: @vw10-1080 0;
        }
    }
  }
}

@media all and (max-width: 580px) {
  .footer {
      padding: @vw100-580 0 @vw22-580 0;
      .topFooter {
        .cols {
          margin-left: -@vw22-580;
          margin-bottom: -@vw44-580;
          width: calc(100% ~"+" @vw44-580);
          .col {
            margin: 0 @vw22-580;
            margin-bottom: @vw44-580;
            width: calc(50% ~"-" @vw50-580);
          }
        }
      }
      .bottomFooter {
          .cols {
            &:not(:last-child) {
              margin-bottom: @vw40-580;
            }
            &:first-child {
              .col {
                &:nth-child(2) {
                  text-align: center;
                  width: 100%;
                }
              }
            }
            &:last-child {
              .col {
                &:last-child {
                  text-align: center;
                }
              }
            }
          }
          .col {
            width: 100%;
            &:not(:last-child) {
              margin-bottom: @vw40-580;
            }
            &.logoCol {
              width: 100%;
              padding-left: @vw44-580;
              padding-right: @vw44-580;
            }
          }
          ul {
            li {
              &:not(:last-child) {
                margin-right: @vw22-580;
              }
            }
          }
      }
      .contactCols {
        display: flex;
      }
      .contactCol {
        width: 50%;
        &:not(:last-child) {
          margin-right: 0;
          margin-bottom: @vw40-580;
        }
        .link {
          &:not(:last-child) {
            margin-bottom: @vw10-580;
          }
        }
        .smallTitle {
          margin-bottom: @vw12-580;
        }
      }
      .divider {
        margin: @vw70-580 0;
      }
      .innerMenu {
        h3, li {
          &:not(:last-child) {
              margin-bottom: @vw16-580;
          }
        }
        a {
            padding: @vw10-580 0;
        }
    }
  }
}