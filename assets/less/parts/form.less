// out: false
@import '../vw_values.less';
@import '../constants.less';

.wpcf7-form {
    margin: 0 auto;
    text-align: left;
    width: 70%;
    p {
        margin-bottom: 0;
    }

    label {
        display: none;
    }

    // Input velden
    input.wpcf7-form-control,
    textarea.wpcf7-form-control {
        width: 100%;
        padding: 0.8rem 1rem;
        background: transparent;
        border-color: rgba(@hardWhite, .1);
        .rounded(@vw60);
        color: @primaryColor;
        font-size: @vw16;
        font-family: 'Figtree', sans-serif;
        outline: none;
        letter-spacing: .14em;
        text-transform: uppercase;
        .transition(.3s);
        &::placeholder {
            color: #cccccc;
            opacity: 0.7;
        }
        &:focus {
            border-color: rgba(@primaryColor, .4);
        }
    }

    .wpcf7-form-control-wrap {
        display: inline-block;
        vertical-align: top;
        width: 50%;
        &:not(:last-child) {
            margin-bottom: @vw10;
        }
        &:has([name="email-address"]) {
            width: 100%;
        }
    }

    .wpcf7-not-valid-tip, .wpcf7-response-output {
        margin-top: @vw5;
        color: rgba(@hardWhite, .4);
        background: rgba(255,0,0,.1);
        border: 1px solid rgba(255,0,0,.2) !important;
        .rounded(@vw10);
        padding: @vw10 @vw22 !important;
        font-size: @vw16;
    }

    textarea {
        min-height: @vw100;
        height: @vw100 * 2;
        resize: none;
    }
    .buttonWrapper {
        margin-top: @vw40;
        margin-bottom: 0;
        text-align: center;
    }
    // Acceptance wrap full width
    .wpcf7-form-control-wrap[data-name^="acceptance"] {
        width: 100%;
        display: block;
        margin-top: @vw20;
    }

    // Show label for acceptance (override your label { display:none })
    .wpcf7-acceptance .wpcf7-list-item > label {
        display: inline-flex !important;
        align-items: center;
        gap: @vw12;
        cursor: pointer;
    }

    // Hide the native checkbox
    .wpcf7-acceptance input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
        pointer-events: none;
    }

    // Text label styling
    .wpcf7-acceptance .wpcf7-list-item-label {
        display: inline-flex;
        align-items: center;
        font-family: 'Figtree', sans-serif;
        font-size: @vw16;
        letter-spacing: .08em;
        color: @primaryColor;
        text-transform: none;
        line-height: 1.2;

        // Circle before text
        &::before {
        content: '';
        display: inline-block;
        width: @vw22;
        height: @vw22;
        border-radius: 50%;
        border: 2px solid fade(@primaryColor, 80%);
        background: transparent;
        margin-right: @vw12;
        transition: all .25s ease;
        }
    }

    // Hover state for circle
    .wpcf7-acceptance .wpcf7-list-item > label:hover
        .wpcf7-list-item-label::before {
        border-color: @primaryColor;
    }

    // Checked state fill
    .wpcf7-acceptance input[type="checkbox"]:checked
        + .wpcf7-list-item-label::before {
        background: @primaryColor;
        border-color: @primaryColor;
    }

    // Optional: inner dot for contrast
    .wpcf7-acceptance input[type="checkbox"]:checked
        + .wpcf7-list-item-label::after {
        content: '';
        display: inline-block;
        width: @vw8;
        height: @vw8;
        border-radius: 50%;
        background: @backgroundColor;
        margin-left: -(@vw22 + @vw12);
        transform: translateX(@vw11);
    }

}