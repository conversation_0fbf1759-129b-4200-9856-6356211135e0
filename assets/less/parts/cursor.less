// out: false
@import '../vw_values.less';
@import '../constants.less';

.backgroundCursor {
    position: fixed;
    width: 60vw;
    height: 60vw;
    background: rgba(@primaryColor, .3);
    pointer-events: none;
    opacity: 0;
    .filter(blur(@vw50));
    pointer-events: none;
    .rounded(50%);
    top: -30vw;
    left: -30vw;
    -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    transition: opacity 0.3s ease-out;
    &.show {
        opacity: .6;
    }
}

.mainCursor {
    position: fixed;
    width: @vw68;
    height: @vw68;
    line-height: @vw68;
    background: @hardWhite;
    color: @primaryColor;
    pointer-events: none;
    opacity: 0;
    pointer-events: none;
    .rounded(50%);
    top: -@vw34;
    left: -@vw34;
    text-align: center;
    font-size: @vw26;
    &.show {
      opacity: 1;
    }
}

@media all and (max-width: 1080px) {
  .backgroundCursor {
      .filter(blur(@vw50-1080));
  }
  .mainCursor {
      width: @vw68-1080;
      height: @vw68-1080;
      line-height: @vw68-1080;
      top: -@vw34-1080;
      left: -@vw34-1080;
      font-size: @vw26-1080;
  }
}

@media all and (max-width: 580px) {
  .backgroundCursor, .mainCursor {
    display: none;
  }
}