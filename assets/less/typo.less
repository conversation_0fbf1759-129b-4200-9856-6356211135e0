
// out: false
@import 'vw_values.less';
@import 'constants.less'; 

.hugeTitle, .bigTitle, .biggerTitle, .mediumTitle, .normalTitle, .subTitle, .tinyTitle, .textTitle, .smallTitle, .text {
  &.white {
    color: @almostWhite;
  }
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.hugeTitle {
  font-size: @vw100 + @vw20;
  text-decoration: none;
  font-family: "Albra", sans-serif;
  font-weight: 100;
  font-style: normal;
  .transitionMore(opacity, .3s);
  &.bigger {
    font-size: @vw100 + @vw100;
  }
}

.bigTitle {
  font-size: @vw65;
  font-family: "Albra", sans-serif;
  font-weight: 100;
  font-style: normal;
  text-transform: capitalize;
}

.signatureTitle {
  font-size: @vw95;
  line-height: 1.2;
  font-family: "Absolute Beauty", sans-serif;
  font-weight: 100;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.mediumTitle {
  font-size: @vw55;
  font-family: "Albra", sans-serif;
  font-weight: 100;
  font-style: normal;
  text-transform: capitalize;
}

.normalMediumTitle {
  font-size: @vw50;
  line-height: 1.2;
  font-family: "Albra", sans-serif;
  font-weight: 100;
  font-style: normal;
  text-transform: capitalize;
  strong {
    color: @primaryColor;
    font-weight: 100;
  }
  p {
    line-height: inherit;
  }
}

.normalTitle {
  font-size: @vw40;
  font-family: "Albra", sans-serif;
  font-weight: 100;
  font-style: normal;
}

.subTitle {
  font-size: @vw26;
  line-height: 1.2;
  font-family: "Figtree", sans-serif;
  font-weight: 200;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.textTitle {
  font-size: @vw16;
  line-height: 1.2;
  font-family: "Figtree", sans-serif;
  font-weight: 400;
  font-style: normal;
  text-transform: uppercase;
  letter-spacing: 0.14em;
  &.smaller {
    font-size: @vw12;
  }
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.smallTitle {
  font-size: @vw22;
  line-height: 1.2;
  font-family: "Figtree", sans-serif;
  font-weight: 400;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

p {
  color: rgba(@secondaryColor, .7);
}

.text {
  &.primary {
    p {
      color: @primaryColor;
    }
  }
  &.white {
    p {
      color: @grey;
    }
  }
  &:not(:first-child) {
    margin-top: @vw20;
  }
  p {
    line-height: 1.5;
    font-weight: 300;
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}

@media all and (max-width: 1080px) {
  .hugeTitle {
    font-size: @vw100-1080 * 1.2;
  }

  .bigTitle {
    font-size: @vw82-1080;
    &.compact {
      font-size: @vw70-1080;
    }
  }

  .mediumTitle {
    font-size: @vw42-1080;
    strong {
      font-size: @vw40-1080;
    }
  }

  .subTitle {
    font-size: @vw24-1080;
  }

  .tinyTitle {
    font-size: @vw16-1080;
  }

  .normalTitle {
    font-size: @vw32-1080;
  }
  .smallTitle {
    font-size: @vw22-1080;
  }

  .signatureTitle {
    font-size: @vw50-1080;
  }

  .text {
    &.bigger {
      font-size: @vw22-1080;
      p {
        font-size: @vw22-1080;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-1080;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1080;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: @vw70-580;
  }

  .bigTitle {
    font-size: @vw70-580;
    &.compact {
      font-size: @vw70-580;
    }
  }

  .mediumTitle {
    font-size: @vw50-580;
    strong {
      font-size: @vw48-580;
    }
  }

  .subTitle {
    font-size: @vw26-580;
  }

  .normalTitle {
    font-size: @vw32-580;
  }

  .smallTitle {
    font-size: @vw22-580;
  }

  .tinyTitle {
    font-size: @vw16-580;
  }

  .signatureTitle {
    font-size: @vw60-580;
  }

  .text {
    &.bigger {
      font-size: @vw22-580;
      p { 
        font-size: @vw22-580;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-580;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }
}
