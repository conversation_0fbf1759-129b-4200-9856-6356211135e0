.hugeTitle.white,
.bigTitle.white,
.biggerTitle.white {
  color: #FFFFFF;
}
.hugeTitle {
  font-size: 12.153vw;
  text-decoration: none;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: italic;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.hugeTitle.link {
  cursor: pointer;
  color: #ECBF6A;
}
.hugeTitle.link:hover {
  opacity: 0.6;
}
.hugeTitle.link span {
  cursor: pointer;
}
.bigTitle {
  font-size: 6.25vw;
  letter-spacing: -6px;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: normal;
}
.bigTitle.compact {
  font-size: 7.523vw;
  text-transform: uppercase;
  letter-spacing: 0;
  font-family: "owners-xxnarrow", sans-serif;
  font-weight: 800;
  font-style: normal;
}
.signatureTitle {
  font-size: 5.498vw;
  line-height: 1.2;
  font-family: "Absolute Beauty", sans-serif;
  font-weight: 100;
  font-style: normal;
}
.signatureTitle.primary {
  color: #ECBF6A;
}
.signatureTitle.secondary {
  color: #63865F;
}
.mediumTitle {
  font-size: 4.051vw;
  letter-spacing: 0;
  font-family: "Figtree", sans-serif;
  font-weight: 700;
  font-style: normal;
  line-height: 1;
}
.mediumTitle strong {
  font-family: 'Buster Brush', cursive;
  font-size: 3.935vw;
  font-weight: 300;
  font-style: normal;
  line-height: 1;
  color: #ECBF6A;
}
.normalTitle {
  font-size: 1.852vw;
  letter-spacing: 0;
  font-family: "Figtree", sans-serif;
  font-weight: 500;
  font-style: normal;
  line-height: 1;
}
.subTitle {
  font-size: 2.083vw;
  line-height: 1.2;
  font-family: "Figtree", sans-serif;
  font-weight: 400;
  font-style: normal;
}
.subTitle.primary {
  color: #ECBF6A;
}
.subTitle.secondary {
  color: #63865F;
}
.smallTitle {
  font-size: 1.273vw;
  line-height: 1.2;
  font-family: "Figtree", sans-serif;
  font-weight: 400;
  font-style: normal;
}
.smallTitle.primary {
  color: #ECBF6A;
}
.smallTitle.secondary {
  color: #63865F;
}
.text.white p {
  color: #C2C2B5;
}
.text:not(:first-child) {
  margin-top: 1.157vw;
}
.text p {
  line-height: 1.5;
  font-weight: 300;
}
.text p:not(:last-child) {
  margin-bottom: 1.273vw;
}
@media all and (max-width: 1080px) {
  .hugeTitle {
    font-size: 6.667vw;
  }
  .bigTitle {
    font-size: 7.593vw;
  }
  .bigTitle.compact {
    font-size: 6.481vw;
  }
  .mediumTitle {
    font-size: 4.63vw;
  }
  .subTitle {
    font-size: 2.222vw;
  }
  .tinyTitle {
    font-size: 1.481vw;
  }
  .text.bigger {
    font-size: 2.037vw;
  }
  .text.bigger p {
    font-size: 2.037vw;
  }
  .text:not(:first-child) {
    margin-top: 1.852vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 2.037vw;
  }
}
@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: 6.034vw;
  }
  .bigTitle {
    font-size: 12.069vw;
  }
  .bigTitle.compact {
    font-size: 12.069vw;
  }
  .mediumTitle {
    font-size: 8.62vw;
  }
  .subTitle {
    font-size: 4.137vw;
  }
  .tinyTitle {
    font-size: 2.758vw;
  }
  .text.bigger {
    font-size: 3.793vw;
  }
  .text.bigger p {
    font-size: 3.793vw;
  }
  .text:not(:first-child) {
    margin-top: 3.448vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 3.793vw;
  }
}
