<?php
// One-time seeder for dummy content and pages. Runs on admin_init.
add_action('admin_init', function(){
  if (!current_user_can('manage_options')) return;
  if (get_option('gift631_seed_completed')) return;

  // Create dummy CPT items
  $img_id = 0;
  // Events
  for($i=1;$i<=3;$i++){
    $id = wp_insert_post(['post_type'=>'event','post_title'=>"Dummy Event $i",'post_status'=>'publish']);
    if($id){ update_field('event_date', date('Y-m-d', strtotime("+{$i} week")),$id);
      update_field('event_location', 'Amsterdam', $id);
      update_field('event_short_description', 'Korte omschrijving van het event.', $id);
    }
  }
  // Testimonials
  for($i=1;$i<=3;$i++){
    $id = wp_insert_post(['post_type'=>'testimonial','post_title'=>"Testimonial $i",'post_status'=>'publish']);
    if($id){ update_field('testimonial_name', "Naam $i", $id);
      update_field('testimonial_role', "Functie/Bedrijf $i", $id);
      update_field('testimonial_quote', 'Dit is een quote voor de testimonial.', $id);
    }
  }
  // Partners
  for($i=1;$i<=3;$i++){
    $id = wp_insert_post(['post_type'=>'partner','post_title'=>"Partner $i",'post_status'=>'publish']);
    if($id){ update_field('partner_link', 'https://example.com', $id); }
  }
  // Services
  for($i=1;$i<=4;$i++){
    $id = wp_insert_post(['post_type'=>'service','post_title'=>"Service $i",'post_status'=>'publish']);
    if($id){ update_field('service_description', 'Korte omschrijving van de service.', $id); }
  }
  // News
  for($i=1;$i<=3;$i++){
    wp_insert_post(['post_type'=>'post','post_title'=>"News item $i",'post_status'=>'publish','post_content'=>'Lorem ipsum dolor sit amet.']);
  }

  // Helper to create a page with flexible content rows
  $make_page = function($title,$slug,$layouts){
    $page_id = get_page_by_path($slug) ? get_page_by_path($slug)->ID : 0;
    if(!$page_id){ $page_id = wp_insert_post(['post_type'=>'page','post_title'=>$title,'post_name'=>$slug,'post_status'=>'publish']); }
    if($page_id && function_exists('acf')){
      $rows = [];
      foreach($layouts as $layout){ $rows[] = ['acf_fc_layout'=>$layout]; }
      update_field('page_sections',$rows,$page_id);
    }
    return $page_id;
  };

  // Pages structure
  $home = $make_page('Home','home',[ 'gift-big-header','gift-partners-marquee','gift-services','gift-title-text','gift-sticky-big-media','gift-big-text-marquee','gift-event','gift-media-slider','gift-information','gift-media-text','gift-latest-news-slider','gift-testimonials' ]);
  if($home){ update_option('page_on_front',$home); update_option('show_on_front','page'); }

  $make_page('News','news',[ 'gift-big-header','gift-latest-news-slider' ]);
  $make_page('About us','about-us',[ 'gift-big-header','gift-intro-text','gift-media-slider','gift-text-grid','gift-sticky-big-media','gift-timeline','gift-credentials','gift-approach','gift-usps','gift-intro-text','gift-media-grid','gift-ctas' ]);
  $make_page('What we do','what-we-do',[ 'gift-big-header','gift-intro-text','gift-media-slider','gift-title-text-list','gift-sticky-big-media','gift-upcoming-events' ]);
  $make_page('Events','events',[ 'gift-big-header','gift-media-text-small','gift-testimonial-highlight','gift-event-information' ]);
  $make_page('Resources','resources',[ 'gift-big-header','gift-intro-text','gift-media-text-small' ]);
  $make_page('Privacy statement','privacy-statement',[ 'gift-title-text-list' ]);

  update_option('gift631_seed_completed',1);
});

// One-time block content seeder to ensure pages have Gutenberg ACF blocks in post_content
add_action('admin_init', function(){
  if (!current_user_can('manage_options')) return;
  if (get_option('gift631_seed_blocks_completed')) return;
  $home_id = get_option('page_on_front');
  if ($home_id) {
    $content = get_post_field('post_content', $home_id);
    // Only seed if no ACF blocks present yet
    if (strpos($content, '<!-- wp:acf/') === false) {
      $mk = function($slug, $data = array()){
        $attrs = array(
          'name' => 'acf/' . $slug,
          'data' => $data,
          'mode' => 'preview'
        );
        return '<!-- wp:acf/' . $slug . ' ' . wp_json_encode($attrs) . ' /-->';
      };
      $blocks = array(
        $mk('gift-big-header'),
        $mk('gift-title-text'),
        $mk('gift-media-text'),
        $mk('gift-sticky-big-media'),
        $mk('gift-big-text-marquee'),
        $mk('gift-event'),
        $mk('gift-media-slider'),
        $mk('gift-information'),
        $mk('gift-testimonials')
      );
      $new_content = implode("\n\n", $blocks);
      wp_update_post(array('ID' => $home_id, 'post_content' => $new_content));
    }
  }
  update_option('gift631_seed_blocks_completed', 1);
});


