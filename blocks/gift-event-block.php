<?php
$event = get_field('event_post');
$e = $event ? get_post($event) : null;
if(!$e){ $ev = get_posts(['post_type'=>'event','posts_per_page'=>1,'orderby'=>'meta_value','meta_key'=>'event_date','order'=>'ASC']); $e = $ev? $ev[0]:null; }
if(!$e) return;
$date = get_field('event_date', $e->ID);
$loc = get_field('event_location', $e->ID);
$short = get_field('event_short_description', $e->ID);
$link = get_permalink($e->ID);
?>
<section class="giftEventBlock <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="event">
      <div class="meta"><span class="date"><?= esc_html($date) ?></span><?php if($loc):?> · <span class="loc"><?= esc_html($loc) ?></span><?php endif; ?></div>
      <h3 class="mediumTitle"><a href="<?= esc_url($link) ?>"><?= esc_html(get_the_title($e)) ?></a></h3>
      <?php if($short): ?><div class="text"><?= wpautop(esc_html($short)) ?></div><?php endif; ?>
      <p><a class="textLink" href="<?= esc_url($link) ?>"><span class="innerText">Meer info</span><span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span></a></p>
    </div>
  </div>
</section>

