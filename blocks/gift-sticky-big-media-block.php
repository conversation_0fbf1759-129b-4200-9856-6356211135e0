<?php
$images = get_field('images');
$opposite = get_field('opposite');
$video = get_field('video');
?>
<section class="giftStickyBigMedia <?= esc_attr(gift631_get_block_classes()) ?>" data-init data-opposite="<?= $opposite ? '1':'0' ?>">
  <div class="contentWrapper">
    <?php if ($video): ?>
      <div class="mediaWrapper">
        <div class="innerImage">
          <video autoplay muted loop playsinline>
            <source src="<?= esc_url($video['url']) ?>" type="video/mp4">
          </video>
        </div>
      </div>
    <?php else: ?>
      <?php if ($images): ?>
        <?php $counter = 0; ?>
        <div class="mediaWrapper">
          <div class="innerImage">
              <?php foreach ($images as $img): $i = optimize_images_for_compressx($img); ?>
                <div class="item <? if ($counter == 0 ) echo 'active'; ?>"><img class="lazy" data-src="<?= esc_url($i['sizes']['large'] ?? $i['url']) ?>" alt=""></div>
                <?php $counter++; ?>
              <?php endforeach; ?>
          </div>
        </div>
      <?php endif; ?>
    <?php endif; ?>
  </div>
</section>

