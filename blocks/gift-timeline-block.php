<?php
$title = get_field('title');
$items = get_field('items');
?>
<section class="giftTimeline <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper smaller">
    <?php if (!empty($title)): ?><h2 class="normalTitle centered" data-lines data-words><?= esc_html($title) ?></h2><?php endif; ?>
    <div class="timeline">
      <?php
      $i = 0;
      if (is_array($items) && !empty($items)) {
        foreach ($items as $it) {
          $i++;
          $side = ($i % 2 === 1) ? 'left' : 'right';
          $date = $it['date'] ?? '';
          $t = $it['title'] ?? '';
          $txt = $it['text'] ?? '';
          $image = $it['image'] ?? null;
          $video = $it['video'] ?? '';
          ?>
          <article class="item side-<?= esc_attr($side) ?>" data-init>
            <div class="content">
              <?php if ($date): ?><div class="date textTitle primary"><?= esc_html($date) ?></div><?php endif; ?>
              <?php if ($t): ?><h3 class="title normalTitle primary"><?= esc_html($t) ?></h3><?php endif; ?>
              <?php if ($txt): ?><div class="text"><?= wpautop(wp_kses_post($txt)) ?></div><?php endif; ?>
            </div>
            <?php if ($image || $video): ?>
              <div class="media">
                <div class="innerImage">
                  <?php if (!empty($video)): ?>
                    <video src="<?= esc_url($video) ?>" autoplay muted loop playsinline></video>
                  <?php elseif (!empty($image)): $img = optimize_images_for_compressx($image); ?>
                    <img class="lazy" data-src="<?= esc_url($img['sizes']['large'] ?? $img['url']) ?>" alt="">
                  <?php endif; ?>
                </div>
              </div>
            <?php endif; ?>
          </article>
          <?php
        }
      } elseif (have_rows('items')) {
        while (have_rows('items')) { the_row(); $i++; $side = ($i % 2 === 1) ? 'left' : 'right';
          $date = get_sub_field('date');
          $t = get_sub_field('title');
          $txt = get_sub_field('text');
          $image = get_sub_field('image');
          $video = get_sub_field('video');
          ?>
          <article class="item side-<?= esc_attr($side) ?>" data-init>
            <div class="content">
              <?php if ($date): ?><div class="date textTitle primary"><?= esc_html($date) ?></div><?php endif; ?>
              <?php if ($t): ?><h3 class="title normalTitle"><?= esc_html($t) ?></h3><?php endif; ?>
              <?php if ($txt): ?><div class="text"><?= wpautop(wp_kses_post($txt)) ?></div><?php endif; ?>
            </div>
            <?php if ($image || $video): ?>
              <div class="media">
                <div class="innerImage">
                  <?php if ($video): ?>
                    <video src="<?= esc_url($video) ?>" autoplay muted loop playsinline></video>
                  <?php else: $img = optimize_images_for_compressx($image); ?>
                    <img class="lazy" data-src="<?= esc_url($img['sizes']['large'] ?? $img['url']) ?>" alt="">
                  <?php endif; ?>
                </div>
              </div>
            <?php endif; ?>
          </article>
          <?php
        }
      }
      ?>
    </div>
  </div>
</section>
