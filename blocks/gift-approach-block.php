<?php
$items = get_field('items');
?>
<section class="giftApproachBlock <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col">
        <div class="sectionHeader" data-sticky-top>
          <h2 class="normalMediumTitle"><?= do_shortcode(wp_kses_post(get_field('title'))) ?></h2>
          <div class="text"><p><?php the_field('text') ?></p></div>
          <?php if(get_field('button')): render_button_from_array(get_field('button'), "whiteText"); endif; ?>
        </div>
      </div>
      <div class="col">
        <?php if ($items): ?>
          <?php foreach ($items as $item): ?>
            <div class="service" data-init>
              <div class="innerCol">
                <div class="innerWrapper">
                  <?php if (!empty($item['subtitle'])): ?>
                    <div class="textTitle smaller"><?= esc_html($item['subtitle']) ?></div>
                  <?php endif; ?>
                  <?php if (!empty($item['title'])): ?>
                    <h3 class="normalTitle"><?= esc_html($item['title']) ?></h3>
                  <?php endif; ?>
                  <?php if (!empty($item['text'])): ?>
                    <div class="text"><?= wpautop(wp_kses_post($item['text'])) ?></div>
                  <?php endif; ?>
                  </div>
                  <?php if (!empty($item['bottom_quote'])): ?>
                    <div class="bottomQuote"><p>“<?= esc_html($item['bottom_quote']) ?>”</p></div>
                  <?php endif; ?>
              </div>
              <div class="innerCol imageCol">
                <?php if (!empty($item['video'])): ?>
                  <video src="<?= esc_url($item['video']) ?>" autoplay muted loop playsinline></video>
                <?php elseif (!empty($item['image'])): ?>
                  <?php $img = $item['image']; ?>
                  <img class="lazy" data-src="<?= esc_url($img['sizes']['medium_large'] ?? $img['url']) ?>" alt="<?= esc_attr($item['title']) ?>">
                <?php endif; ?>
              </div>
            </div>
          <?php endforeach; ?>
        <?php endif; ?>
      </div>
    </div>
  </div>
</section>
