<?php
$items = get_field('items'); // repeater of {title,text}
?>
<section class="giftTextGrid <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="grid">
      <?php if($items): foreach($items as $it): ?>
        <div class="cell">
          <?php if(!empty($it['title'])): ?><h3 class="normalTitle"><?= esc_html($it['title']) ?></h3><?php endif; ?>
          <?php if(!empty($it['text'])): ?><div class="text"><?= wpautop(wp_kses_post($it['text'])) ?></div><?php endif; ?>
        </div>
      <?php endforeach; endif; ?>
    </div>
  </div>
</section>

