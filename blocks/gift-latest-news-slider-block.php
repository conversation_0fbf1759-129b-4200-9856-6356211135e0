<?php
$news = get_posts(array(
  'post_type' => 'post',
  'posts_per_page' => 6,
  'orderby' => 'date',
  'order' => 'DESC'
));
$title = get_field('title');
$text = get_field('text');
?>
<section class="giftLatestNews <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="col">
      <?php if ($title): ?><h2 class="bigTitle" data-lines data-words><?= esc_html($title) ?></h2><?php endif; ?>
    </div>
    <div class="col">
      <div class="text"><?php echo wpautop(wp_kses_post($text)); ?></div>
      <?php if(get_field('button')): render_button_from_array(get_field('button'), "outline"); endif; ?>
    </div>
    <div class="sliderWrapper">
      <div class="slider" data-slider data-loop-slider="true">
        <?php foreach ($news as $n): ?>
          <div class="slide">
            <a href="<?= esc_url(get_permalink($n)) ?>" class="news">
              <div class="imageWrapper">
                <div class="innerImage">
                  <img class="lazy" data-src="<?= esc_url(get_the_post_thumbnail_url($n, 'medium_large')) ?>" alt="<?= esc_attr(get_the_title($n)) ?>">
                </div>
              </div>
              <div class="info">
                <div class="textTitle"><?= esc_html(get_the_date('', $n)) ?></div>
                <h3 class="normalTitle"><?= esc_html(get_the_title($n)) ?></h3>
              </div>
            </a>
          </div>
        <?php endforeach; ?>
      </div>
      <div class="sliderIndicator">
        <div class="innerBar"></div>
      </div>
      <div class="sliderButton arrowButton prev" data-prev><i class="icon-arrow-left"></i><i class="icon-arrow-left"></i></div>
      <div class="sliderButton arrowButton next" data-next><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></div>
    </div>
  </div>
</section>

