<?php
$testimonials = get_posts(array(
  'post_type' => 'testimonial',
  'posts_per_page' => 3,
  'orderby' => 'rand',
  'order' => 'random',
));
?>
<section class="giftTestimonialsBlock <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="col">
        <div class="sectionHeader" data-sticky-top>
          <h2 class="bigTitle" data-lines data-words><?php the_field('title') ?></h2>
          <div class="text"><p><?php the_field('text') ?></p></div>
          <?php if(get_field('button')): render_button_from_array(get_field('button'), "outline"); endif; ?>
        </div>
    </div>
    <div class="col">
      <?php foreach($testimonials as $t): setup_postdata($t); $photo=get_field('testimonial_photo', $t->ID); ?>
        <article class="testimonial" data-init>
          <h3 class="normalTitle">
              <?php echo get_the_title($t->ID); ?>
          </h3>
          <div class="quote text"><p><?php the_field('testimonial_quote', $t->ID); ?></p></div>
          <div class="meta">
            <div class="photo"><?php if($photo): $i=optimize_images_for_compressx($photo); ?><img class="lazy" data-src="<?= esc_url($i['sizes']['thumbnail'] ?? $i['url']) ?>" alt="<?php echo esc_attr(get_the_title($t->ID)) ?>"><?php endif; ?></div>
            <div class="innerContent">
              <h4 class="textTitle primary"><?php the_field('testimonial_name', $t->ID); ?></h4>
              <h5 class="textTitle"><?php the_field('testimonial_role', $t->ID); ?></h5>
            </div>
          </div>
        </article>
        <?php wp_reset_postdata(); ?>
      <?php endforeach; ?>
    </div>
  </div>
</section>

