<?php
$title = get_field('title');
$subtitle = get_field('subtitle');
$background = get_field('image'); // image
?>
<section class="giftEventsHeaderBlock dark" data-init>
  <div class="contentWrapper">
    <div class="innerContent">
      <?php if ($title): ?><h1 class="hugeTitle" data-lines data-words><?= esc_html($title) ?></h1><?php endif; ?>
      <?php if(get_field('button')): render_button_from_array(get_field('button')); endif; ?>
    </div>
    <?php if ($background): $img = optimize_images_for_compressx($background); ?>
    <div class="imageWrapper">
      <div class="innerImage">
        <?php if(get_field('video')): ?>
          <video autoplay muted loop playsinline>
            <source src="<?= esc_url(get_field('video')['url']) ?>" type="video/mp4">
          </video>
        <?php else: ?>
        <img class="lazy" data-src="<?= esc_url($img['sizes']['full'] ?? $img['url']) ?>" alt="<?= esc_attr($title ?: 'Header') ?>" />
        <?php endif; ?>
      </div>
    </div>
    <?php endif; ?>
  </div>
</section>

