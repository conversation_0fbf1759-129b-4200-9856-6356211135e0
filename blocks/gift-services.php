<?php
$loop = new WP_Query(array(
  'post_type' => 'service',
  'posts_per_page' => -1,
  'orderby' => 'menu_order title',
  'order' => 'ASC'
));
?>
<section class="giftServices <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="grid">
      <?php while($loop->have_posts()): $loop->the_post(); $icon = get_field('service_icon'); ?>
        <div class="service">
          <div class="icon">
            <?php if ($icon): $img = optimize_images_for_compressx($icon); ?>
              <img src="<?= esc_url($img['sizes']['medium'] ?? $img['url']) ?>" alt="<?= esc_attr(get_the_title()) ?>">
            <?php endif; ?>
          </div>
          <h3 class="normalTitle"><?= esc_html(get_the_title()) ?></h3>
          <div class="text"><?php the_field('service_description'); ?></div>
        </div>
      <?php endwhile; wp_reset_postdata(); ?>
    </div>
  </div>
</section>

