<?php
$title = get_field('title');
$subtitle = get_field('subtitle');
$background = get_field('background'); // image
?>
<section class="giftBigHeader dark <?= get_field("text_position") ?>" data-init>
  <?php if ($background): $img = optimize_images_for_compressx($background); ?>
  <div class="backgroundWrapper">
    <div class="innerImage">
      <img class="lazy" data-src="<?= esc_url($img['sizes']['full'] ?? $img['url']) ?>" alt="<?= esc_attr($title ?: 'Header') ?>" />
    </div>
  </div>
  <?php endif; ?>
  <div class="contentWrapper">
    <div class="innerContent">
      <?php if ($title): ?><h1 class="bigTitle" data-lines data-words><?= esc_html($title) ?></h1><?php endif; ?>
      <?php if ($subtitle): ?><div class="subTitle primary"><?= esc_html($subtitle) ?></div><?php endif; ?>
      <?php if(get_field('button')): render_button_from_array(get_field('button')); endif; ?>
    </div>
  </div>
</section>

