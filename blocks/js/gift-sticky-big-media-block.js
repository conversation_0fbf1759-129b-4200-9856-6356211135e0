$(document).ready(function() {
    $(document).on("initPage", function() {
        if ($(".giftStickyBigMedia").length > 0) {
            initStickyBigMedia();
        }
    });
});

function initStickyBigMedia() { 
    $(".giftStickyBigMedia").each(function(i, el){
        var stickyElement = $(el).find(".mediaWrapper");
        var sizeCalculation = stickyElement.find(".item").length == 1 ? 3 : 5;
        $(el).css("height", $(el).outerHeight() * sizeCalculation);
        setTimeout(function() {
            gsap.to(stickyElement, .01, {
                width: 100 + "%",
                scrollTrigger: {
                    start: "center center",
                    end: "top+=" + ($(el).outerHeight() - $('header').outerHeight()) + " top",
                    trigger: stickyElement,
                    scrub: true,
                    onUpdate(self) {
                        var items = stickyElement.find(".item");
                        if (items.length == 1) return;
                        var activeItem = Math.floor((items.length ) * self.progress);
                        items.removeClass("active");
                        items.eq(activeItem).addClass("active");
                    }
                }
            });
        }, 100);
    });

    var $textWrapper = $('.giftStickyBigMedia .mediaWrapper');
    var $imageWrapper = $('.giftStickyBigMedia .contentWrapper');
    var wrapperHeight = $imageWrapper.outerHeight();
    var originalOffsetTop = $textWrapper.offset().top;
    var textWrapperHeight = $textWrapper.outerHeight();

    scroller.on('scroll', function() {
        textWrapperHeight = $textWrapper.outerHeight();
        var scrollTop = $(window).scrollTop() + ($("header").outerHeight() * 1.5);
        var stickyStart = originalOffsetTop;
        var stickyEnd = stickyStart + wrapperHeight - textWrapperHeight;

        if (scrollTop >= stickyStart && scrollTop <= stickyEnd) {
        $textWrapper.css({
            top: (scrollTop - stickyStart) + 'px'
        });
        } else if (scrollTop > stickyEnd) {
        $textWrapper.css({
            top: (wrapperHeight - textWrapperHeight) + 'px'
        });
        } else {
        $textWrapper.css({
            top: '0px'
        });
        }
    });
}