// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftApproachBlock {
    .cols {
        display: flex;
        flex-wrap: wrap;
    }
    .sectionHeader {
        position: absolute;
        top: 0;
        left: 0;
        padding-right: @vw100 + @vw30;
    }
    .col {
        position: relative;
        display: inline-block;
        width: 50%;
        vertical-align: top;
    }
    .button {
        margin-top: @vw30;
    }
    .service {
        display: flex;
        color: @secondaryColor;
        gap: @vw16;
        flex-direction: row;
        opacity: 0;
        .transform(translateY(@vw22));
        p {
            color: rgba(@secondaryColor, .7);
        }
        &.inview {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
        }
        &:not(:last-child) {
            margin-bottom: @vw50;
        }
        .normalTitle {
            line-height: 1.2;
        }
        .textTitle {
            color: rgba(@secondaryColor, .3);
            margin-bottom: @vw12;
        }
        .bottomQuote {
            background: @primaryColor;
            padding: @vw22 @vw30;
            width: 100%;
            margin-top: @vw30;
            p {
                color: rgba(@almostWhite, .7);
            }
        }
        .innerCol {
            width: 50%;
            .rounded(@vw20);
            position: relative;
            overflow: hidden;
            &:not(.imageCol) {
                background: @hardWhite;
                .innerWrapper {
                    padding: @vw30;
                }
            }
            &.imageCol {
                overflow: hidden;
                position: relative;
                img, video {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }       
        }
    }
}