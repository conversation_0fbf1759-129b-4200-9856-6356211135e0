// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftSignupBlock {
    position: relative;
    .backgroundImage {
        position: absolute;
        bottom: -(@vw100 * 2.2);
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1));
        -webkit-mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1));
        video, img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: bottom;
            position: absolute;
            bottom: 0;
            left: 0;
        }
    }
    .cols {
        display: flex;
        gap: @vw16;
    }
    .col {
        width: 100%;
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        .sectionHeader {
            margin-bottom: @vw50;
            text-align: center;
        }
    }
    form {
        .mediumTitle {
            display: none;
        }
    }
}

@media all and (max-width: 1080px) {
    .goatContactBlock {
        .cols {
            gap: @vw16-1080;
        }
        .col {
            width: 50%;
            .rounded(@vw22-1080);
            &:not(.imageCol) {
                padding: @vw80-1080 @vw44-1080;
                &:before {
                    backdrop-filter: blur(@vw30-1080);
                }
            }
            .sectionHeader {
                margin-bottom: @vw60-1080;
            }
        }
        .wpcf7-form {
            p {
                margin-bottom: 1.5rem;
            }
            input.wpcf7-form-control,
            textarea.wpcf7-form-control {
                padding: 0.8rem 1rem;
                .rounded(@vw10-1080);
                font-size: @vw22-1080;
            }

            .wpcf7-form-control-wrap {
                &:not(:last-child) {
                    margin-bottom: @vw10-1080;
                }
            }

            .wpcf7-not-valid-tip {
                margin-top: @vw5-1080;
                .rounded(@vw10-1080);
                padding: @vw10-1080 @vw22-1080;
                font-size: @vw16-1080;
            }

            textarea {
                min-height: @vw100-1080;
                height: @vw100-1080 * 2;
            }

            // Response bericht
            .wpcf7-response-output {
                margin-top: @vw22-1080;
                padding: @vw10-1080;
                .rounded(@vw10-1080);
            }
            .buttonWrapper {
                margin-top: @vw40-1080;
            }
        }
    }
}

@media all and (max-width: 580px) {
  .goatContactBlock {
        .cols {
            gap: @vw16-580;
        }
        .col {
            width: 100%;
            .rounded(@vw22-580);
            &.imageCol {
                display: none;
            }
            &:not(.imageCol) {
                padding: @vw80-580 @vw22-580;
                &:before {
                    backdrop-filter: blur(@vw30-580);
                }
            }
            .sectionHeader {
                margin-bottom: @vw60-580;
            }
        }
        .wpcf7-form {
            p {
                margin-bottom: 1.5rem;
            }
            input.wpcf7-form-control,
            textarea.wpcf7-form-control {
                padding: 0.8rem 1rem;
                .rounded(@vw10-580);
                font-size: @vw22-580;
            }

            .wpcf7-form-control-wrap {
                &:not(:last-child) {
                    margin-bottom: @vw10-580;
                }
            }

            .wpcf7-not-valid-tip {
                margin-top: @vw5-580;
                .rounded(@vw10-580);
                padding: @vw10-580 @vw22-580;
                font-size: @vw16-580;
            }

            textarea {
                min-height: @vw100-580;
                height: @vw100-580 * 2;
            }

            // Response bericht
            .wpcf7-response-output {
                margin-top: @vw22-580;
                padding: @vw10-580;
                .rounded(@vw10-580);
            }
            .buttonWrapper {
                margin-top: @vw40-580;
                .button {
                    width: 100%;
                    .body {
                        width: 100%;
                    }
                }
            }
        }
    }
}