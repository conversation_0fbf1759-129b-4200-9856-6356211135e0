// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftTestimonialsBlock {
    .cols {
        display: flex;
        flex-wrap: wrap;
    }
    .sectionHeader {
        position: absolute;
        top: 0;
        left: 0;
        padding-right: @vw100 + @vw30;
    }
    .col {
        position: relative;
        display: inline-block;
        width: 50%;
        vertical-align: top;
    }
    .button {
        margin-top: @vw30;
    }
    .testimonial {
        padding: @vw50;
        .rounded(@vw14);
        background: #FFFFFF;
        display: block;
        opacity: 0;
        .transform(translateY(@vw22));
        &.inview {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
        }
        &:not(:last-child) {
            margin-bottom: @vw50;
        }
        .meta {
            margin-top: @vw40;
            width: 60%;
            .photo {
                display: inline-block;
                width: @vw76;
                height: @vw76;
                overflow: hidden;
                position: relative;
                vertical-align: middle;
                margin-right: @vw25;
                .rounded(50%);
                img {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }
            .innerContent {
                display: inline-block;
                vertical-align: middle;
                width: calc(100% ~"-" @vw120);
            }
        }
    }
}