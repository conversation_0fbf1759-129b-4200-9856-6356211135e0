// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftIntroText {
    &.inview {
        .innerCol {
            p {
                opacity: 1;
                .transform(translateY(0));
                .stagger(100, 0.15s, 0.9s);
            }
        }
    }
    &.centered {
        .normalMediumTitle {
            text-align: center;
            width: 100%;
        }
        .innerCol {
            margin-left: 0;
            width: 100%;
            padding-right: 0;
        }
    }
    .normalMediumTitle {
        width: 80%;
    }
   .innerCol {
    display: inline-block;
    margin-top: @vw100 + @vw22;
    margin-left: 50%;
    width: 50%;
    padding-right: (@vw100 + @vw100);
    p {
        .transform(translateY(@vw22));
        opacity: 0;
        transition: opacity .45s, transform .45s;
        -webkit-transition: opacity .45s, transform .45s;
    }
   }
}