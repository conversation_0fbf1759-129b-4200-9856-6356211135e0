// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftCredentials {
    &.inview {
        .credential {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
            .stagger(100, 0.15s, 0.9s);
        }
    }
    .mediumTitle {
        text-align: center;
        margin-bottom: @vw100;
    }
    .credentials {
        margin: -@vw8 0;
        display: flex;
        width: calc(100% ~"+" @vw16);
        margin-left: -@vw8;
    }
    .credential {
        border: 1px solid @primaryColor;
        .rounded(@vw14);
        display: inline-block;
        padding: @vw22;
        vertical-align: top;
        width: calc(20% ~"-" @vw16);
        margin: @vw8;
        flex-direction: column;
        opacity: 0;
        .transform(translateY(@vw22));
        .normalTitle {
            margin-bottom: @vw50;
        }
        .text {
            margin: 0;
        }
        .button {
            margin-top: @vw22;
        }
    }
}