// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftEventsHeaderBlock {
    margin-top: 0 !important;
    position: relative;
    overflow: hidden;
    min-height: @vw100 * 7;
    height: 100lvh;
    &.inview {
        .subTitle, .button {
            opacity: 1;
            .transform(translateY(0));
            .stagger(100, 0.15s, 0.9s);
        }
        .backgroundWrapper {
            .innerImage {
                opacity: 1;
                .transform(translate(-50%, -50%) scale(1));
                transition: opacity .45s, transform .45s;
                -webkit-transition: opacity .45s, transform .45s;
            }
        }
    }
    .contentWrapper {
        position: relative;
        height: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;
        padding-bottom: @vw60;
    }
    .backgroundWrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        .innerImage {
            position: absolute;
            top: 50%;
            left: 50%;
            .transform(translate(-50%, -50%) scale(1.1));
            width: 100%;
            height: 100%;
            opacity: 0;
            img, video {
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: @vw100 * 2.2;
            background: linear-gradient(rgba(#000000, 0), rgba(#000000, 1));
        }
    }
    .innerContent {
        width: 100%;
    }
    .subTitle {
        margin: @vw44 0;
        padding-right: @vw60;
    }
    .subTitle, .button {
        opacity: 0;
        .transform(translateY(@vw22));
        transition: opacity .45s, transform .45s;
        -webkit-transition: opacity .45s, transform .45s;
    }
}