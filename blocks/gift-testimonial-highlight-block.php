<?php
$limit = get_field('limit') ?: 3;
$rand = new WP_Query(['post_type'=>'testimonial','posts_per_page'=>$limit,'orderby'=>'rand']);
?>
<section class="giftTestimonialHighlight <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="slider" data-slider>
      <?php while($rand->have_posts()): $rand->the_post(); $photo=get_field('testimonial_photo'); ?>
        <div class="item">
          <blockquote class="quote"><?php the_field('testimonial_quote'); ?></blockquote>
          <div class="meta">
            <div class="photo"><?php if($photo): $i=optimize_images_for_compressx($photo); ?><img src="<?= esc_url($i['sizes']['thumbnail'] ?? $i['url']) ?>" alt=""><?php endif; ?></div>
            <div><strong><?php the_field('testimonial_name'); ?></strong><?php $r=get_field('testimonial_role'); if($r):?> — <?= esc_html($r) ?><?php endif; ?></div>
          </div>
        </div>
      <?php endwhile; wp_reset_postdata(); ?>
    </div>
  </div>
</section>

