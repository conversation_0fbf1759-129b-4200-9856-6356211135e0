<?php
$credentials = get_field('items'); // repeater images
?>
<section class="giftCredentials <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <h2 class="mediumTitle" data-lines data-words><?php the_field('title') ?></h2>
    <div class="credentials"> 
         <?php if($credentials): foreach($credentials as $c): ?>
          <div class="credential">
            <?php if(!empty($c['title'])):?><h3 class="normalTitle"><?= esc_html($c['title']) ?></h3><?php endif; ?>
            <?php if(!empty($c['text'])):?><div class="text"><?= wpautop(wp_kses_post($c['text'])) ?></div><?php endif; ?>
            <?php if(!empty($c['button'])): render_button_from_array($c['button'], "outline"); endif; ?>
          </div>
        <?php endforeach; endif; ?>
    </div>
  </div>
</section>

