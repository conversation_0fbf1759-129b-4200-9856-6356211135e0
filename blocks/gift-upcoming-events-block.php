<?php
$limit = get_field('limit') ?: 3;
$events = get_posts([
  'post_type'=>'event','posts_per_page'=>$limit,
  'meta_key'=>'event_date','orderby'=>'meta_value','order'=>'ASC',
  'meta_query'=>[[ 'key'=>'event_date','value'=>date('Y-m-d'),'compare'=>'>=','type'=>'DATE' ]]
]);
?>
<section class="giftUpcomingEvents <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="list">
      <?php foreach($events as $e): $date=get_field('event_date',$e->ID); $loc=get_field('event_location',$e->ID); ?>
        <a class="item" href="<?= esc_url(get_permalink($e)) ?>">
          <span class="date"><?= esc_html($date) ?></span>
          <span class="title"><?= esc_html(get_the_title($e)) ?></span>
          <?php if($loc): ?><span class="loc"><?= esc_html($loc) ?></span><?php endif; ?>
        </a>
      <?php endforeach; ?>
    </div>
  </div>
</section>

