<?php
$items = get_field('items'); // repeater of images
?>
<section class="giftMediaGrid <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="items">
      <?php if($items): foreach($items as $item): ?>
        <?php $video = $item['video'] ?? null; $image = $item['image'] ?? null; ?>
        <div class="item">
          <div class="imageWrapper">
            <div class="innerImage">
              <?php if($video): ?>
                <video src="<?= esc_url($video) ?>" autoplay muted loop playsinline></video>
              <?php else: $i = optimize_images_for_compressx($image); ?>
                <img class="lazy" data-src="<?= esc_url($i['sizes']['medium_large'] ?? $i['url']) ?>" alt="">
              <?php endif; ?>
            </div>
          </div>
        </div>
      <?php endforeach; endif; ?>
    </div>
  </div>
</section>

