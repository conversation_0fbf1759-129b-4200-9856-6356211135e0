<?php
$title = get_field('title');
$list = get_field('list'); // repeater: title, text
?>
<section class="giftTitleTextList <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper smaller">
    <?php if ($title): ?><h2 class="mediumTitle"><?= esc_html($title) ?></h2><?php endif; ?>
    <?php if ($list): ?>
      <div class="list">
        <?php foreach ($list as $row): ?>
          <div class="item">
            <?php if (!empty($row['title'])): ?><h3 class="normalTitle"><?= esc_html($row['title']) ?></h3><?php endif; ?>
            <?php if (!empty($row['text'])): ?><div class="text"><?php echo wpautop(wp_kses_post($row['text'])); ?></div><?php endif; ?>
          </div>
        <?php endforeach; ?>
      </div>
    <?php endif; ?>
  </div>
</section>

