<?php
$title = get_field('title');
$improvements = get_field('improvements'); // top row repeater: icon + text
$stats = get_field('stats'); // bottom row repeater: title + text
$button = get_field('button');
?>
<section class="giftUSPs <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper smallest">
    <?php if ($title): ?>
      <h2 class="normalMediumTitle" data-lines data-words><?= esc_html($title) ?></h2>
    <?php endif; ?>
  </div>
  <div class="contentWrapper smaller">
    <?php if ($improvements): ?>
      <div class="improvements">
        <?php foreach ($improvements as $item): $icon = $item['icon'] ?? null; ?>
          <div class="improvement" data-init>
            <?php if ($icon): $img = optimize_images_for_compressx($icon); ?>
              <div class="icon"><img class="lazy" data-src="<?= esc_url($img['sizes']['medium'] ?? $img['url']) ?>" alt=""></div>
            <?php endif; ?>
            <?php if (!empty($item['text'])): ?><div class="normalTitle"><?= esc_html($item['text']) ?></div><?php endif; ?>
          </div>
        <?php endforeach; ?>
      </div>
    <?php endif; ?>

    <?php if ($stats): ?>
      <div class="stats">
        <?php foreach ($stats as $s): ?>
          <div class="stat" data-init>
            <?php if (!empty($s['title'])): ?><h3 class="mediumTitle primary"><?= esc_html($s['title']) ?></h3><?php endif; ?>
            <?php if (!empty($s['text'])): ?><div class="text"><?= wpautop(wp_kses_post($s['text'])) ?></div><?php endif; ?>
          </div>
        <?php endforeach; ?>
      </div>
    <?php endif; ?>

    <?php if ($button): ?>
      <div class="cta">
        <?php render_button_from_array($button, "whiteText"); ?>
      </div>
    <?php endif; ?>
  </div>
</section>
