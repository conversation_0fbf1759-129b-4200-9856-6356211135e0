<?php
$slides = get_field('slides');
$title = get_field('title');
$text = get_field('text');
?>
<section class="giftMediaSlider <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <? if ($title || $text): ?>
    <div class="col">
      <?php if ($title): ?><h2 class="bigTitle" data-lines data-words><?= esc_html($title) ?></h2><?php endif; ?>
    </div>
    <div class="col">
      <div class="text"><?php echo wpautop(wp_kses_post($text)); ?></div>
      <?php if(get_field('button')): render_button_from_array(get_field('button'), "outline"); endif; ?>
    </div>
    <? endif; ?>
    <div class="sliderWrapper">
      <div class="slider" data-slider data-loop-slider="true">
        <?php foreach ($slides as $s): ?>
          <div class="slide">
            <div class="imageWrapper">
              <div class="innerImage">
                  <?php if($s['video']): ?>
                  <video autoplay muted loop playsinline>
                    <source src="<?= esc_url($s['video']['url']) ?>" type="video/mp4">
                  </video>
                  <?php else: ?>
                  <img class="lazy" data-src="<?= esc_url($s['image']['sizes']['large']) ?>" alt="<?= esc_attr($s['image']['alt']) ?>">
                  <?php endif; ?>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
      </div>
      <div class="sliderIndicator">
        <div class="innerBar"></div>
      </div>
      <div class="sliderButton arrowButton prev" data-prev><i class="icon-arrow-left"></i><i class="icon-arrow-left"></i></div>
      <div class="sliderButton arrowButton next" data-next><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></div>
    </div>
  </div>
</section>

