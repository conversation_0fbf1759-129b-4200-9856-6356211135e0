<?php get_header(); ?>

<div class="event-detail">
        <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
            <section class="goatSmallHeaderBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
                <div class="backgroundWrapper">
                    <!-- get featured image of the event post -->
                     <?php if (has_post_thumbnail()) : ?>
                        <img src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" class="bgImage">
                    <?php endif; ?>
                </div>
                <div class="contentWrapper smaller">
                    <?php if (get_field('event_date')) : ?>
                        <div class="subTitle primary" data-lines data-words>
                            <?php echo get_field('event_date'); ?>
                        </div>
                    <?php endif; ?>
                    <h1 class="mediumTitle" data-lines data-words><?php the_title(); ?></h1>
                </div>
            </section>


            <section class="goatTextTwoColumnBlock" data-init 
                <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
                <div class="contentWrapper smaller">
                    <div class="cols">
                        <?php
                        $description = get_field('event_description');
                        $description = wpautop($description); // Zorg dat het nette <p>'s zijn
                        $description = wp_kses_post($description); // Beveiliging

                        // Vind alle <p>...</p>
                        preg_match_all('/<p[^>]*>.*?<\/p>/is', $description, $matches);
                        $paragraphs = $matches[0] ?? [];

                        // Split in twee helften
                        $half = ceil(count($paragraphs) / 2);
                        $firstCol = array_slice($paragraphs, 0, $half);
                        $secondCol = array_slice($paragraphs, $half);
                        ?>
                        
                        <div class="col">
                            <div class="text">
                                <?php echo implode('', $firstCol); ?>
                            </div>
                        </div>
                        <div class="col small">
                            <div class="text">
                                <?php echo implode('', $secondCol); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </section>



            <div class="event-content">
                <?php the_content(); ?>
            </div>

            <?php if (get_field('event_images')) : 
                $images = get_field('event_images');
                if ($images): ?>
                    <section class="goatBigImageSliderBlock">
                        <div class="contentWrapper">
                            <div class="sliderWrapper">
                                <div class="slider" data-slider data-loop-slider="true">
                                    <?php foreach ($images as $img): ?>
                                        <div class="slide">
                                            <div class="imageWrapper">
                                                <div class="innerImage">
                                                    <?php
                                                    $img = optimize_images_for_compressx($img);
                                                    ?>
                                                    <img class="lazy"
                                                        data-src="<?= esc_url($img['sizes']['large']) ?>"
                                                        alt="<?= esc_attr($name) ?>" />
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if (count($images) > 1): ?>
                                <div class="sliderButton arrowButton prev" data-prev><i class="icon-arrow-left"></i><i class="icon-arrow-left"></i></div>
                                <div class="sliderButton arrowButton next" data-next><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </section>
                <?php endif; ?>
            <?php endif; ?>

            

            <?php if (get_field('event_registration_link')) : ?>
                <div class="event-registration">
                    <?php 
                    $link = get_field('event_registration_link');
                    if ($link) : ?>
                        <a href="<?php echo esc_url($link['url']); ?>" class="button event-register-btn" target="<?php echo esc_attr($link['target'] ?: '_self'); ?>">
                            <span class="innerText"><?php echo esc_html($link['title']); ?></span>
                            <span class="arrows">
                                <i class="icon-arrow-right-up"></i>
                                <i class="icon-arrow-right-up"></i>
                            </span>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif;
            $events = get_posts(array(
                'post_type' => 'event',
                'posts_per_page' => 3,
                'post_status' => 'publish',
                'meta_key' => 'event_date',
                'orderby' => 'meta_value',
                'order' => 'ASC',
                'post__not_in' => array(get_the_ID())
            ));
            if ($events) : ?>
                <section class="relatedEventsBlock" data-init>
                    <div class="contentWrapper">
                        <div class="events" data-show-mouse>
                            <?php
                                if ($events) : 
                                    foreach ($events as $event) : 
                                        $event_id = $event->ID;
                                ?>
                                <a href="<?php echo get_permalink($event_id); ?>" class="event">
                                    <div class="imageWrapper">
                                        <div class="innerImage">
                                            <img class="lazy" data-src="<?php echo get_the_post_thumbnail_url($event_id, 'medium_large'); ?>" alt="<?php echo esc_attr(get_the_title($event_id)); ?>">
                                        </div>
                                    </div>
                                    <div class="eventInfo">
                                        <?php if (get_field('event_date', $event_id)) : ?>
                                            <div class="smallTitle primary"><?php echo get_field('event_date', $event_id); ?></div>
                                        <?php endif; ?>
                                        <h3 class="eventTitle normalTitle"><?php echo get_the_title($event_id); ?></h3>
                                    </div>
                                </a>
                                <?php 
                                    endforeach;
                                endif; 
                                ?>
                            </div>
                        </div>
                        <div class="eventsSlider events sliderWrapper">
                            <div class="slider" data-slider data-loop-slider="true">
                                <?php
                                if ($events) : 
                                    foreach ($events as $event) : 
                                        $event_id = $event->ID;
                                ?>
                                <div class="slide">
                                    <a href="<?php echo get_permalink($event_id); ?>" class="event">
                                        <div class="imageWrapper">
                                            <div class="innerImage">
                                                <img class="lazy" data-src="<?php echo get_the_post_thumbnail_url($event_id, 'medium_large'); ?>" alt="<?php echo esc_attr(get_the_title($event_id)); ?>">
                                            </div>
                                        </div>
                                        <div class="eventInfo">
                                            <?php if (get_field('event_date', $event_id)) : ?>
                                                <div class="smallTitle primary"><?php echo get_field('event_date', $event_id); ?></div>
                                            <?php endif; ?>
                                            <h3 class="eventTitle normalTitle"><?php echo get_the_title($event_id); ?></h3>
                                        </div>
                                    </a>
                                </div>
                                <?php 
                                    endforeach;
                                endif; 
                                ?>
                            </div>
                            <div class="sliderButton arrowButton prev" data-prev><i class="icon-arrow-left"></i><i class="icon-arrow-left"></i></div>
                            <div class="sliderButton arrowButton next" data-next><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></div>
                        </div>
                    </div>
                </section>
            <?php endif; ?>

        <?php endwhile; endif; ?>
    </div>
</div>

<?php get_footer(); ?>
