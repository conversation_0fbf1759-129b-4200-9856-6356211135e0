<?php

if (!defined('WP_DEBUG') || !WP_DEBUG) {
    error_reporting(E_ERROR | E_PARSE | E_CORE_ERROR | E_COMPILE_ERROR);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// Start output buffering immediately to prevent header issues
ob_start();

// Fix translation loading issues for plugins
add_action('init', function() {
    // Force proper translation loading timing for ACF
    if (function_exists('acf')) {
        load_plugin_textdomain('acf', false, dirname(plugin_basename(__FILE__)) . '/languages/');
    }

    // Force proper translation loading timing for Limit Login Attempts
    if (function_exists('limit_login_attempts_reloaded')) {
        load_plugin_textdomain('limit-login-attempts-reloaded', false, dirname(plugin_basename(__FILE__)) . '/languages/');
    }
}, 1);

// Prevent early translation loading warnings
add_filter('override_load_textdomain', function($override, $domain, $mofile) {
    if (in_array($domain, ['acf', 'limit-login-attempts-reloaded'])) {
        // Only allow loading during proper init or later
        if (!did_action('init')) {
            return true; // Prevent early loading
        }
    }
    return $override;
}, 10, 3);

// Clean output buffer at the end of page load
add_action('wp_footer', function() {
    if (ob_get_level()) {
        ob_end_flush();
    }
}, 999);

// Additional header protection
add_action('wp_loaded', function() {
    if (!headers_sent()) {
        // Ensure no premature output
        while (ob_get_level()) {
            ob_end_clean();
        }
        ob_start();
    }
});

// Custom error handler to suppress specific WordPress notices
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    // Suppress specific WordPress translation warnings
    if (strpos($errstr, '_load_textdomain_just_in_time was called incorrectly') !== false) {
        return true; // Suppress this error
    }

    // Suppress header modification warnings in development
    if (strpos($errstr, 'Cannot modify header information') !== false) {
        return true; // Suppress this error
    }

    // Let other errors through to default handler
    return false;
}, E_WARNING | E_NOTICE);

// Restore error handler after WordPress loads
add_action('wp_loaded', function() {
    restore_error_handler();
}, 999);

// Plugin-specific fixes for common issues
add_action('plugins_loaded', function() {
    // Fix ACF translation loading
    if (class_exists('ACF')) {
        remove_action('plugins_loaded', 'acf_load_textdomain');
        add_action('init', 'acf_load_textdomain', 5);
    }

    // Fix Limit Login Attempts translation loading
    if (function_exists('limit_login_attempts_reloaded_init')) {
        // Ensure proper timing for this plugin
        remove_action('plugins_loaded', 'limit_login_attempts_reloaded_init');
        add_action('init', 'limit_login_attempts_reloaded_init', 5);
    }
}, 1);

// Prevent any premature output that could cause header issues
add_action('send_headers', function() {
    if (ob_get_level() === 0) {
        ob_start();
    }
});

// Final cleanup before headers are sent
add_action('wp', function() {
    // Clean any unwanted output
    if (ob_get_level()) {
        $content = ob_get_clean();
        // Only keep content that doesn't contain error messages
        if (!preg_match('/Notice:|Warning:|Fatal error:/', $content)) {
            echo $content;
        }
        ob_start();
    }
});

// Additional safety net for admin area
if (is_admin()) {
    add_action('admin_init', function() {
        // Suppress admin notices for translation issues
        add_filter('gettext', function($translation, $text, $domain) {
            if (strpos($text, '_load_textdomain_just_in_time was called incorrectly') !== false) {
                return ''; // Return empty string to hide the message
            }
            return $translation;
        }, 10, 3);
    });
}

// Only force HTTPS on production to avoid local certificate issues
$__gift_env = function_exists('wp_get_environment_type') ? wp_get_environment_type() : 'production';
if ($__gift_env === 'production') {
    add_action('template_redirect', function() {
        if (!is_admin() && !wp_doing_ajax() && !is_ssl()) {
            $redirect_url = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            wp_redirect($redirect_url, 301);
            exit();
        }
    });

    add_filter('home_url', function($url) {
        return str_replace('http://', 'https://', $url);
    });

    add_filter('site_url', function($url) {
        return str_replace('http://', 'https://', $url);
    });
}


// Spotify credentials apart includen zodat ze niet in versiebeheer hoeven
if (file_exists(__DIR__ . '/spotify-credentials.php')) {
    require_once __DIR__ . '/spotify-credentials.php';
}

function jn_enqueue_assets() {
    // Critical scripts that need to load immediately
    $critical_scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'main_js' => '/assets/js/main.js',
    );

    // Non-critical scripts that can be deferred
    $deferred_scripts = array(
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'HAMMER' => '/libs/hammer.min.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'FLICKITY' => '/libs/flickity.min.js',
        'SPLITTEXT' => '/libs/SplitText.min.js',
        'Header' => '/assets/js/header.js',
        'Footer' => '/assets/js/footer.js',
        'Menu' => '/assets/js/parts/menu.js',
        'Parallax' => '/assets/js/parts/parallax.js',
        'Gallery' => '/assets/js/parts/gallery.js',
        'Sticky' => '/assets/js/parts/sticky.js',
        'Cursor' => '/assets/js/parts/cursor.js',
        'Slider' => '/assets/js/parts/slider.js',
        'Split' => '/assets/js/parts/split.js',
        'Marquee' => '/assets/js/parts/marquee.js',
        'Rotate' => '/assets/js/parts/rotate.js',
        'CookieBanner' => '/assets/js/parts/cookiebanner.js',
    );

    // Enqueue critical scripts first (blocking)
    foreach ($critical_scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', false); // Load in head
    }

    // Enqueue deferred scripts (non-blocking)
    foreach ($deferred_scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true); // Load in footer
    }

    // include blocks javascript files
    $block_scripts = array(
        'gift-sticky-big-media' => '/blocks/js/gift-sticky-big-media-block.js',
        'gift-header-block' => '/blocks/js/gift-header-block.js',
    );
    foreach ($block_scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true); // Load in footer
    }


    // Conditional block loading - placeholder for gift631 blocks (no per-block JS yet)
    $blocks = array();
    // If you add per-block JS later, populate $blocks accordingly.


    wp_enqueue_style('main', get_stylesheet_uri());
    wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
    wp_enqueue_style('cookiebanner', get_theme_file_uri('/assets/css/cookiebanner.css'), array(), '1.0', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add defer attribute to non-critical scripts
function add_defer_attribute($tag, $handle, $src) {
    $deferred_scripts = array(
        'Lenis', 'Swup', 'Swup_head', 'Swup_Gtag', 'Select2', 'HAMMER',
        'ScrollTrigger', 'Custom_Ease', 'FLICKITY', 'SPLITTEXT', 'Header',
        'Footer', 'Menu', 'Parallax', 'Gallery', 'Cursor', 'Slider',
        'Split', 'Marquee', 'CookieBanner'
    );

    if (in_array($handle, $deferred_scripts)) {
        return str_replace('<script ', '<script defer ', $tag);
    }

    return $tag;
}
add_filter('script_loader_tag', 'add_defer_attribute', 10, 3);

// Add preconnect hints for performance
function add_preconnect_hints() {
    echo '<link rel="preconnect" href="https://region1.google-analytics.com" crossorigin>';
    echo '<link rel="preconnect" href="https://www.googletagmanager.com" crossorigin>';
    echo '<link rel="preconnect" href="https://use.typekit.net" crossorigin>';
    echo '<link rel="preconnect" href="https://p.typekit.net" crossorigin>';
    echo '<link rel="preconnect" href="https://widget.bandsintown.com" crossorigin>';
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
}
add_action('wp_head', 'add_preconnect_hints', 1);

// Add compression headers for better performance (optimized for CompressX.io)
function add_compression_headers() {
    if (!is_admin()) {
        // Enable Gzip compression for non-image content
        if (function_exists('gzencode') && !ob_get_level() &&
            !preg_match('/\.(jpg|jpeg|png|gif|webp|avif)$/i', $_SERVER['REQUEST_URI'])) {
            ob_start('ob_gzhandler');
        }

        // Add cache headers for static assets (but let CompressX.io handle images)
        if (strpos($_SERVER['REQUEST_URI'], '/assets/') !== false ||
            strpos($_SERVER['REQUEST_URI'], '/libs/') !== false) {
            header('Cache-Control: public, max-age=31536000'); // 1 year
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        }

        // For image requests, ensure Vary: Accept is set for CompressX.io
        if (preg_match('/\.(jpg|jpeg|png|gif|webp|avif)$/i', $_SERVER['REQUEST_URI'])) {
            header('Vary: Accept');
        }
    }
}
add_action('init', 'add_compression_headers');

// Remove unused WordPress features to reduce JS/CSS
function remove_unused_wp_features() {
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('admin_print_styles', 'print_emoji_styles');

    // Remove WordPress version
    remove_action('wp_head', 'wp_generator');

    // Remove RSD link
    remove_action('wp_head', 'rsd_link');

    // Remove wlwmanifest link
    remove_action('wp_head', 'wlwmanifest_link');

    // Remove shortlink
    remove_action('wp_head', 'wp_shortlink_wp_head');

    // Remove feed links
    remove_action('wp_head', 'feed_links', 2);
    remove_action('wp_head', 'feed_links_extra', 3);
}
add_action('init', 'remove_unused_wp_features');

// Optimize caching and enable back/forward cache
function optimize_caching_headers() {
    if (!is_admin()) {
        // Remove cache-control: no-store to enable back/forward cache
        header_remove('Cache-Control');

        // Set appropriate cache headers based on content type
        if (is_front_page() || is_page()) {
            header('Cache-Control: public, max-age=3600, must-revalidate'); // 1 hour for pages
        } elseif (is_singular('artist') || is_singular('post')) {
            header('Cache-Control: public, max-age=7200, must-revalidate'); // 2 hours for content
        }

        // Add ETag for better caching
        $etag = md5(get_the_modified_time('U') . get_queried_object_id());
        header('ETag: "' . $etag . '"');

        // Handle conditional requests
        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && $_SERVER['HTTP_IF_NONE_MATCH'] === '"' . $etag . '"') {
            header('HTTP/1.1 304 Not Modified');
            exit;
        }
    }
}
add_action('template_redirect', 'optimize_caching_headers');

// Add service worker for better caching (optional)
function add_service_worker_support() {
    if (!is_admin()) {
        echo '<script>
        if ("serviceWorker" in navigator) {
            window.addEventListener("load", function() {
                navigator.serviceWorker.register("/sw.js").then(function(registration) {
                    console.log("SW registered: ", registration);
                }).catch(function(registrationError) {
                    console.log("SW registration failed: ", registrationError);
                });
            });
        }
        </script>';
    }
}
// Uncomment the line below if you want to add service worker support
// add_action('wp_footer', 'add_service_worker_support');

// Optimize database queries
function optimize_database_queries() {
    // Remove unnecessary queries
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);

    // Limit post revisions
    if (!defined('WP_POST_REVISIONS')) {
        define('WP_POST_REVISIONS', 3);
    }

    // Disable pingbacks
    add_filter('xmlrpc_enabled', '__return_false');
    add_filter('wp_headers', function($headers) {
        unset($headers['X-Pingback']);
        return $headers;
    });
}
add_action('init', 'optimize_database_queries');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-noise' => 'Noise',
        'customTheme-main-callout-displacement' => 'Displacement image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-mission-statement' => 'Mission statement',
        'customTheme-main-callout-value-proposition' => 'Value proposition',
        'customTheme-main-callout-address' => 'Address',
        'customTheme-main-callout-signature' => 'Signature',
        'customTheme-main-callout-signature-link' => 'Signature link',
        'customTheme-main-callout-footer-form' => 'Footer form',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-tiktok' => 'Tiktok URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        // Ondersteuning voor array met type/label
        if (is_array($label)) {
            $control_args = array(
                'label' => $label['label'],
                'section' => 'customTheme-main-callout-section',
                'settings' => $setting_id
            );
            if ($label['type'] === 'image') {
                $control_args['width'] = 750;
                $control_args['height'] = 500;
                $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
            } else if ($label['type'] === 'textarea') {
                $control_args['type'] = 'textarea';
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            } else if ($label['type'] === 'password') {
                $control_args['type'] = 'password';
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            } else {
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            }
        } else {
            $control_args = array(
                'label' => $label,
                'section' => 'customTheme-main-callout-section',
                'settings' => $setting_id
            );
            if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false || $setting_id === 'customTheme-main-callout-noise' || $setting_id === 'customTheme-main-callout-displacement') {
                $control_args['width'] = 750;
                $control_args['height'] = 500;
                $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
            }
            elseif ($label === 'Description' || $label === 'Company Information' || $label === 'Artist Contact Text' || $label === 'Mission statement' || $label === 'Value proposition' || $label === 'Address') {
                $control_args['type'] = 'textarea';
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            }
            else {
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            }
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Allow editors to access theme customizer
function allow_editors_customizer_access() {
    $role = get_role('editor');
    if ($role) {
        $role->add_cap('edit_theme_options');
    }
}
add_action('admin_init', 'allow_editors_customizer_access');

// Optimize admin interface for editors
function optimize_admin_for_editors() {
    $current_user = wp_get_current_user();

    // If user is an editor, add helpful admin customizations
    if (in_array('editor', $current_user->roles)) {
        // Add custom admin CSS for better UX
        add_action('admin_head', function() {
            echo '<style>
                /* Highlight Artists and Team menu items for editors */
                #menu-posts-artist .wp-menu-name,
                #menu-posts-team .wp-menu-name {
                    font-weight: bold;
                    color: #0073aa;
                }

                /* Add visual indicator for important sections */
                #menu-posts-artist:before,
                #menu-posts-team:before {
                    content: "★ ";
                    color: #ffb900;
                }
            </style>';
        });

        // Add admin notice with helpful information
        add_action('admin_notices', function() {
            $screen = get_current_screen();
            if ($screen && in_array($screen->post_type, ['artist'])) {
                echo '<div class="notice notice-info is-dismissible">
                    <p><strong>Limitless Editor:</strong> Je kunt hier alle artiesten beheren.</p>
                </div>';
            }
        });
    }
}
add_action('admin_init', 'optimize_admin_for_editors');

// Ensure editors have all necessary capabilities for managing artists and team
function ensure_editor_capabilities() {
    $role = get_role('editor');
    if ($role) {
        // Media capabilities
        $role->add_cap('upload_files');
        $role->add_cap('edit_files');

        // User management for team coordination (limited)
        $role->add_cap('list_users');
        $role->add_cap('edit_users');

        // Menu management for navigation updates
        $role->add_cap('edit_theme_options');
    }
}
add_action('admin_init', 'ensure_editor_capabilities');


// Reorganize admin menu for editors
function reorganize_admin_menu_for_editors() {
    if (current_user_can('edit_posts') && !current_user_can('manage_options')) {
        // Move Artists to top of menu for editors
        add_filter('custom_menu_order', '__return_true');
        add_filter('menu_order', function($menu_order) {
            // Reorder menu items to prioritize content management
            $new_order = array(
                'index.php', // Dashboard
                'edit.php?post_type=artist', // Artists
                'edit.php', // Posts
                'edit.php?post_type=page', // Pages
                'upload.php', // Media
                'customize.php', // Customizer
            );
            return $new_order;
        });
    }
}

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'primary-menu' => 'Primary Menu',
        'footer-menu-1' => 'footer-menu-1',
        'footer-menu-2' => 'footer-menu-2',
        'footer-menu-3' => 'footer-menu-3',
        'footer-menu-4' => 'footer-menu-4',
        'bottom-footer' => 'bottom-footer',
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Add ACF Options Page for Header Settings
if( function_exists('acf_add_options_page') ) {
    acf_add_options_page(array(
        'page_title' => 'Header Instellingen',
        'menu_title' => 'Header',
        'menu_slug' => 'header-settings',
        'capability' => 'edit_posts',
        'icon_url' => 'dashicons-admin-generic',
        'position' => 30,
    ));
}

// Enable ACF Local JSON load/save to make field groups editable in WP Admin
add_filter('acf/settings/save_json', function($path){
  return get_stylesheet_directory() . '/acf-json';
});
add_filter('acf/settings/load_json', function($paths){
  $paths[] = get_stylesheet_directory() . '/acf-json';
  return $paths;
});



// ACF: Header Shop Link field group on the existing Header options page
add_action('acf/init', function(){
  if (!function_exists('acf_add_local_field_group')) return;
  acf_add_local_field_group(array(
    'key' => 'group_gift631_header_shop',
    'title' => 'Header Shop Link',
    'fields' => array(
      array(
        'key' => 'field_gift631_shop_link',
        'label' => 'Shop URL',
        'name' => 'shop_link',
        'type' => 'url',
      )
    ),
    'location' => array(
      array(array('param' => 'options_page','operator' => '==','value' => 'header-settings'))
    ),
  ));
});

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;



}

// blocks

// Ensure custom block category exists for gift631 blocks (inserter + registration)
add_filter('block_categories_all', function($categories, $post){
  $exists = false;
  foreach($categories as $c){ if(isset($c['slug']) && $c['slug']==='gift631-blocks'){ $exists = true; break; } }
  if($exists) return $categories;
  $categories[] = array('slug'=>'gift631-blocks','title'=>__('gift631 Blocks','gift631'),'icon'=>'star-filled');
  return $categories;
}, 10, 2);

// Add gift631 block category


add_action('acf/init', 'gift631_acf_blocks_init');
function gift631_acf_blocks_init() {
    if (!function_exists('acf_register_block_type')) return;

    $blocks = array(
        // Homepage and common blocks
        array('name' => 'gift-big-header', 'title' => __('Big Header', 'gift631'), 'template' => 'blocks/gift-big-header-block.php', 'icon' => 'cover-image'),
        array('name' => 'gift-header', 'title' => __('Header', 'gift631'), 'template' => 'blocks/gift-header-block.php', 'icon' => 'cover-image'),
        array('name' => 'gift-events-header', 'title' => __('Events Header', 'gift631'), 'template' => 'blocks/gift-events-header-block.php', 'icon' => 'cover-image'),
        array('name' => 'gift-partners-marquee', 'title' => __('Partners Marquee', 'gift631'), 'template' => 'blocks/gift-partners-marquee-block.php', 'icon' => 'groups'),
        array('name' => 'gift-signup-block', 'title' => __('Signup Block', 'gift631'), 'template' => 'blocks/gift-signup-block.php', 'icon' => 'email-alt'),
        array('name' => 'gift-services', 'title' => __('Services', 'gift631'), 'template' => 'blocks/gift-services-block.php', 'icon' => 'list-view'),
        array('name' => 'gift-title-text', 'title' => __('Title Text', 'gift631'), 'template' => 'blocks/gift-title-text-block.php', 'icon' => 'text'),
        array('name' => 'gift-sticky-big-media', 'title' => __('Sticky Big Media', 'gift631'), 'template' => 'blocks/gift-sticky-big-media-block.php', 'icon' => 'format-image'),
        array('name' => 'gift-big-text-marquee', 'title' => __('Big Text Marquee', 'gift631'), 'template' => 'blocks/gift-big-text-marquee-block.php', 'icon' => 'megaphone'),
        array('name' => 'gift-event', 'title' => __('Event', 'gift631'), 'template' => 'blocks/gift-event-block.php', 'icon' => 'calendar'),
        array('name' => 'gift-media-slider', 'title' => __('Media Slider', 'gift631'), 'template' => 'blocks/gift-media-slider-block.php', 'icon' => 'images-alt2'),
        array('name' => 'gift-information', 'title' => __('Information', 'gift631'), 'template' => 'blocks/gift-information-block.php', 'icon' => 'info'),
        array('name' => 'gift-media-text', 'title' => __('Media Text', 'gift631'), 'template' => 'blocks/gift-media-text-block.php', 'icon' => 'align-pull-left'),
        array('name' => 'gift-latest-news-slider', 'title' => __('Latest News Slider', 'gift631'), 'template' => 'blocks/gift-latest-news-slider-block.php', 'icon' => 'slides'),
        array('name' => 'gift-testimonials', 'title' => __('Testimonials', 'gift631'), 'template' => 'blocks/gift-testimonials-block.php', 'icon' => 'testimonial'),
        array('name' => 'gift-intro-text', 'title' => __('Intro Text', 'gift631'), 'template' => 'blocks/gift-intro-text-block.php', 'icon' => 'welcome-learn-more'),

        array('name' => 'gift-text-grid', 'title' => __('Text Grid', 'gift631'), 'template' => 'blocks/gift-text-grid-block.php', 'icon' => 'screenoptions'),
        array('name' => 'gift-timeline', 'title' => __('Timeline', 'gift631'), 'template' => 'blocks/gift-timeline-block.php', 'icon' => 'schedule'),
        array('name' => 'gift-credentials', 'title' => __('Credentials', 'gift631'), 'template' => 'blocks/gift-credentials-block.php', 'icon' => 'awards'),
        array('name' => 'gift-approach', 'title' => __('Approach', 'gift631'), 'template' => 'blocks/gift-approach-block.php', 'icon' => 'admin-generic'),
        array('name' => 'gift-usps', 'title' => __('USPs', 'gift631'), 'template' => 'blocks/gift-usps-block.php', 'icon' => 'yes'),
        array('name' => 'gift-media-grid', 'title' => __('Media Grid', 'gift631'), 'template' => 'blocks/gift-media-grid-block.php', 'icon' => 'grid-view'),
        array('name' => 'gift-ctas', 'title' => __('Call to Actions', 'gift631'), 'template' => 'blocks/gift-ctas-block.php', 'icon' => 'button'),
        array('name' => 'gift-title-text-list', 'title' => __('Title Text List', 'gift631'), 'template' => 'blocks/gift-title-text-list-block.php', 'icon' => 'editor-ul'),
        array('name' => 'gift-upcoming-events', 'title' => __('Upcoming Events', 'gift631'), 'template' => 'blocks/gift-upcoming-events-block.php', 'icon' => 'calendar-alt'),
        array('name' => 'gift-media-text-small', 'title' => __('Media Text Small', 'gift631'), 'template' => 'blocks/gift-media-text-small-block.php', 'icon' => 'align-left'),
        array('name' => 'gift-testimonial-highlight', 'title' => __('Testimonial Highlight', 'gift631'), 'template' => 'blocks/gift-testimonial-highlight-block.php', 'icon' => 'star-filled'),
        array('name' => 'gift-event-information', 'title' => __('Event Information', 'gift631'), 'template' => 'blocks/gift-event-information-block.php', 'icon' => 'calendar'),
        array('name' => 'gift-privacy-statement', 'title' => __('Privacy Statement', 'gift631'), 'template' => 'blocks/gift-privacy-statement-block.php', 'icon' => 'shield'),
    );

    foreach ($blocks as $b) {
        acf_register_block_type(array(
            'name' => $b['name'],
            'title' => $b['title'],
            'render_template' => $b['template'],
            'category' => 'gift631-blocks',
            'icon' => $b['icon'],
            'supports' => array('align' => false, 'anchor' => true),
            'mode' => 'preview',
        ));
    }
}

function render_button($field_name, $classes = "") {
    $link = get_field($field_name);

    if (is_array($link) && isset($link['url'], $link['title'])) {
        $link_url = esc_url($link['url']);
        $link_title = esc_html($link['title']);
        $link_target = !empty($link['target']) ? esc_attr($link['target']) : '_self';

        // based on less buttons.less:
        echo '<a class="button ' . $classes . '" href="' . $link_url . '" title="' . $link_title . '" target="' . $link_target . '">
            <span class="hiddenText" aria-hidden="true">' . $link_title . '</span>
            <span class="innerTexts">
                an class="innerText" aria-hidden="true" data-words>' . $link_title . '</span>
                <span class="innerText" aria-hidden="true" data-words>' . $link_title . '</span>
            </span>
        </a>';
    }
}

function render_button_from_array($button, $classes = "") {
    if (is_array($button) && isset($button['url'], $button['title'])) {
        $link_url = esc_url($button['url']);
        $link_title = esc_html($button['title']);
        $link_target = !empty($button['target']) ? esc_attr($button['target']) : '_self';

        // based on less buttons.less:
        echo '<a class="button ' . $classes . '" href="' . $link_url . '" title="' . $link_title . '" target="' . $link_target . '">
            <span class="hiddenText">' . $link_title . '</span>
            <span class="innerTexts">
                <span class="innerText" aria-hidden="true" data-words>' . $link_title . '</span>
                <span class="innerText" aria-hidden="true" data-words>' . $link_title . '</span>
            </span>
        </a>';
    }
}

add_filter('wpcf7_form_elements', function ($form) {
    // Voeg de form-titel boven het formulier toe (wanneer beschikbaar)
    if (class_exists('WPCF7_ContactForm')) {
        $contact_form = WPCF7_ContactForm::get_current();
        if ($contact_form && method_exists($contact_form, 'title')) {
            $title = $contact_form->title();
            if (!empty($title)) {
                $form = '<div class="cf7-form-title mediumTitle">' . esc_html($title) . '</div>' . $form;
            }
        }
    }

    // Vervang de standaard submit input door een custom button en behoud het meegegeven label
    $form = preg_replace_callback(
        '/<input\\b[^>]*type\\s*=\\s*["\']submit["\'][^>]*>/i',
        function ($matches) {
            $label = 'Submit';
            if (preg_match('/value\\s*=\\s*["\']([^"\']+)["\']/', $matches[0], $m)) {
                $label = $m[1];
            }

            $label_attr = esc_attr($label);
            $label_html = esc_html($label);

            return '<div class="buttonWrapper"><a class="button primary" href="#" onclick="this.closest(\'form\').submit(); return false;" title="' . $label_attr . '">' .
                '<span class="hiddenText">' . $label_html . '</span>' .
                '<span class="innerTexts">' .
                    '<span class="innerText" aria-hidden="true" data-words>' . $label_html . '</span>' .
                    '<span class="innerText" aria-hidden="true" data-words>' . $label_html . '</span>' .
                '</span>' .
            '</a></div>';
        },
        $form
    );

    return $form;
});


function render_text_link($field_name) {
    $link = get_field($field_name);
    if ($link) {
        $link_url = $link['url'];
        $link_title = $link['title'];
        $link_target = $link['target'] ? $link['target'] : '_self';
        echo '<a href="' . esc_url($link_url) . '" title="' . esc_html($link_title) . '" class="textLink" target="' . esc_attr($link_target) . '">
                <span class="innerText">' . esc_html($link_title) . '</span>
                <span class="arrows">
                    <i class="icon-arrow-right-up"></i>
                    <i class="icon-arrow-right-up"></i>
                </span>
              </a>';
    }

// Generic render callback for gift631 ACF blocks
function gift631_render_block($block, $content = '', $is_preview = false, $post_id = 0) {
    $slug = str_replace('acf/', '', $block['name']);
    echo "<!-- DEBUG: Rendering block: $slug -->";
    $path_primary = get_theme_file_path("/blocks/{$slug}.php");
    $path_alt = get_theme_file_path("/blocks/{$slug}-block.php");
    if (file_exists($path_primary)) {
        echo "<!-- DEBUG: Including $path_primary -->";
        include $path_primary;
    } elseif (file_exists($path_alt)) {
        echo "<!-- DEBUG: Including $path_alt -->";
        include $path_alt;
    } else {
        echo "<!-- DEBUG: No template found for $slug -->";
        echo '<div style="background:red;color:white;padding:20px;margin:20px;"><h2>BLOCK DEBUG: ' . esc_html($slug) . '</h2><p>Template not found</p></div>';
    }
}

}

function render_text_link_sub($field_name) {
    $link = get_sub_field($field_name);
    if ($link) {
        $link_url = $link['url'];
        $link_title = $link['title'];
        $link_target = $link['target'] ? $link['target'] : '_self';
        echo '<a href="' . esc_url($link_url) . '" title="' . esc_html($link_title) . '" class="textLink" target="' . esc_attr($link_target) . '">
                <span class="innerText">' . esc_html($link_title) . '</span>
                <span class="arrows">
                    <i class="icon-arrow-right-up"></i>
                    <i class="icon-arrow-right-up"></i>
                </span>
              </a>';
    }
}

function custom_theme_setup() {
    add_image_size('project-thumb-mobile', 640, 800, true);
    add_image_size('project-thumb-large', 640, 800, true);

    // Add WebP support
    add_theme_support('post-thumbnails');

    // Enable WebP uploads
    add_filter('wp_check_filetype_and_ext', 'enable_webp_upload', 10, 4);
    add_filter('upload_mimes', 'webp_upload_mimes');
}
add_action('after_setup_theme', 'custom_theme_setup');

// Enable WebP uploads
function enable_webp_upload($data, $file, $filename, $mimes) {
    $filetype = wp_check_filetype($filename, $mimes);
    return [
        'ext'             => $filetype['ext'],
        'type'            => $filetype['type'],
        'proper_filename' => $data['proper_filename']
    ];
}

function webp_upload_mimes($existing_mimes) {
    $existing_mimes['webp'] = 'image/webp';
    return $existing_mimes;
}

// CompressX.io integration - no need for manual WebP generation
// CompressX.io handles WebP/AVIF conversion automatically via server rules

// CompressX.io handles WebP/AVIF serving automatically via Nginx rules
// No need for manual URL manipulation - just return original URL
function get_webp_image_url($image_url) {
    // CompressX.io automatically serves WebP/AVIF via server rules
    // Just return the original URL - server will handle format conversion
    return $image_url;
}

// Add CompressX.io optimization hints
function add_compressx_optimization_headers() {
    if (!is_admin()) {
        // Add Vary: Accept header to help with CompressX.io caching
        if (!headers_sent()) {
            header('Vary: Accept');
        }

        // Add preload hints for critical images
        if (is_front_page()) {
            // Preload critical above-the-fold images
            $critical_images = array();

            // Get homepage header background image
            if (function_exists('get_field')) {
                $background_image = get_field('background_image');
                if ($background_image && isset($background_image['sizes']['large'])) {
                    $critical_images[] = $background_image['sizes']['large'];
                }
            }

            // Get logo images
            $logo_white = wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo-white'));
            if ($logo_white) {
                $critical_images[] = $logo_white;
            }

            // Add preload links for critical images
            foreach ($critical_images as $image_url) {
                if ($image_url) {
                    echo '<link rel="preload" as="image" href="' . esc_url($image_url) . '">' . "\n";
                }
            }
        }
    }
}
add_action('wp_head', 'add_compressx_optimization_headers', 2);

// Optimize images for CompressX.io
function optimize_images_for_compressx($image_array) {
    if (!is_array($image_array) || !isset($image_array['url'])) {
        return $image_array;
    }

    // Add dimensions if missing (helps with layout shifts)
    $image_array = add_image_dimensions($image_array);

    // CompressX.io works best with original URLs - no modifications needed
    // The server-level rules handle format conversion automatically

    return $image_array;
}

// Image optimization for CompressX.io (no dimensions needed)
function add_image_dimensions($image_array) {
    // Just return the image array as-is for CompressX.io compatibility
    // No width/height attributes needed - CSS handles responsive behavior
    return $image_array;
}

// Add ACF field group for Header Options
add_action('acf/init', function() {
    if( function_exists('acf_add_local_field_group') ) {
        // Header Options Field Group
        acf_add_local_field_group(array(
            'key' => 'group_header_options',
            'title' => 'Header Instellingen',
            'fields' => array(
                array(
                    'key' => 'field_header_button',
                    'label' => 'Header Button',
                    'name' => 'header_button',
                    'type' => 'link',
                    'instructions' => 'Configureer de button die in de header wordt weergegeven',
                    'required' => 0,
                    'return_format' => 'array',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'header-settings',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',


            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
        ));
    }
});


// Register custom post types for gift631
add_action('init', function() {
    $common_supports = array('title','editor','thumbnail','excerpt');

    register_post_type('event', array(
        'labels' => array(
            'name' => __('Events','gift631'),
            'singular_name' => __('Event','gift631')
        ),
        'public' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'events'),
        'menu_icon' => 'dashicons-calendar-alt',
        'supports' => $common_supports,
    ));

    register_post_type('partner', array(


        'labels' => array(
            'name' => __('Partners','gift631'),
            'singular_name' => __('Partner','gift631')
        ),
        'public' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'partners'),
        'menu_icon' => 'dashicons-groups',
        'supports' => $common_supports,
    ));

    register_post_type('service', array(
        'labels' => array(
            'name' => __('Services','gift631'),
            'singular_name' => __('Service','gift631')
        ),
        'public' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'services'),
        'menu_icon' => 'dashicons-hammer',
        'supports' => $common_supports,
    ));

    register_post_type('testimonial', array(
        'labels' => array(
            'name' => __('Testimonials','gift631'),
            'singular_name' => __('Testimonial','gift631')
        ),
        'public' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'testimonials'),
        'menu_icon' => 'dashicons-format-quote',
        'supports' => $common_supports,
    ));
});

// ACF field groups for CPTs (minimal)
add_action('acf/init', function() {
    if (!function_exists('acf_add_local_field_group')) return;

    // Events
    acf_add_local_field_group(array(
        'key' => 'group_gift631_event',
        'title' => 'Event Details',
        'fields' => array(
            array(
                'key' => 'field_gift631_event_date',
                'label' => 'Date',
                'name' => 'event_date',
                'type' => 'date_picker',
                'display_format' => 'Y-m-d',
                'return_format' => 'Y-m-d',
            ),
            array(
                'key' => 'field_gift631_event_location',
                'label' => 'Location',
                'name' => 'event_location',
                'type' => 'text',
            ),
            array(
                'key' => 'field_gift631_event_short',
                'label' => 'Short Description',
                'name' => 'event_short_description',
                'type' => 'textarea',
            ),
            array(
                'key' => 'field_gift631_event_video',
                'label' => 'Video URL (optional)',
                'name' => 'event_video_url',
                'type' => 'url',
            ),
        ),
        'location' => array(
            array(array('param' => 'post_type','operator' => '==','value' => 'event'))
        ),
    ));

    // Testimonials
    acf_add_local_field_group(array(
        'key' => 'group_gift631_testimonial',
        'title' => 'Testimonial Details',
        'fields' => array(
            array(
                'key' => 'field_gift631_testimonial_name',
                'label' => 'Name',
                'name' => 'testimonial_name',
                'type' => 'text',
            ),
            array(
                'key' => 'field_gift631_testimonial_role',
                'label' => 'Role/Company',
                'name' => 'testimonial_role',
                'type' => 'text',
            ),
            array(
                'key' => 'field_gift631_testimonial_photo',
                'label' => 'Photo',
                'name' => 'testimonial_photo',
                'type' => 'image',
                'return_format' => 'array',
                'preview_size' => 'medium',
            ),
            array(
                'key' => 'field_gift631_testimonial_quote',
                'label' => 'Quote',
                'name' => 'testimonial_quote',
                'type' => 'textarea',
            ),
        ),
        'location' => array(
            array(array('param' => 'post_type','operator' => '==','value' => 'testimonial'))
        ),
    ));

    // Partners
    acf_add_local_field_group(array(
        'key' => 'group_gift631_partner',
        'title' => 'Partner Details',
        'fields' => array(
            array(
                'key' => 'field_gift631_partner_logo',
                'label' => 'Logo',
                'name' => 'partner_logo',
                'type' => 'image',
                'return_format' => 'array',
                'preview_size' => 'medium',
            ),
            array(
                'key' => 'field_gift631_partner_link',
                'label' => 'Link',
                'name' => 'partner_link',
                'type' => 'url',
            ),
        ),
        'location' => array(
            array(array('param' => 'post_type','operator' => '==','value' => 'partner'))
        ),
    ));

    // Services
    acf_add_local_field_group(array(
        'key' => 'group_gift631_service',
        'title' => 'Service Details',
        'fields' => array(
            array(
                'key' => 'field_gift631_service_icon',
                'label' => 'Icon/Image',
                'name' => 'service_icon',
                'type' => 'image',
                'return_format' => 'array',
                'preview_size' => 'medium',
            ),
            array(
                'key' => 'field_gift631_service_description',
                'label' => 'Description',
                'name' => 'service_description',
                'type' => 'wysiwyg',
                'tabs' => 'all',
                'toolbar' => 'full',
                'media_upload' => 0,
            ),
            array(
                'key' => 'field_gift631_service_show_detail',
                'label' => 'Detailpagina tonen',
                'name' => 'show_detail_page',
                'type' => 'true_false',
                'ui' => 1,
                'default_value' => 0,
            ),
        ),
        'location' => array(
            array(array('param' => 'post_type','operator' => '==','value' => 'service'))
        ),
    ));
});

// Flexible Content field for pages with layouts mapping to blocks
add_action('acf/init', function(){
  if (!function_exists('acf_add_local_field_group')) return;
  $layouts = array(
    'gift-big-header','gift-header', 'gift-events-header','gift-partners-marquee','gift-signup-block','gift-services','gift-title-text','gift-sticky-big-media',
    'gift-big-text-marquee','gift-event','gift-media-slider','gift-information','gift-media-text',
    'gift-latest-news-slider','gift-testimonials','gift-intro-text','gift-text-grid','gift-timeline',
    'gift-credentials','gift-approach','gift-usps','gift-media-grid','gift-ctas','gift-title-text-list',
    'gift-upcoming-events','gift-media-text-small','gift-testimonial-highlight','gift-event-information','gift-privacy-statement'
  );
  $acf_layouts = array();
  foreach($layouts as $l){
    $acf_layouts[] = array('key'=>'layout_'.$l,'name'=>$l,'label'=>ucwords(str_replace('-',' ',$l)),'display'=>'block','sub_fields'=>array());
  }
  acf_add_local_field_group(array(
    'key'=>'group_gift631_page_sections','title'=>'Page Sections','fields'=>array(
      array('key'=>'field_gift631_page_sections','label'=>'Sections','name'=>'page_sections','type'=>'flexible_content','layouts'=>$acf_layouts,'button_label'=>'Add Section')
    ),
    'location'=>array(array(array('param'=>'post_type','operator'=>'==','value'=>'page')))
  ));
});


// Sticky next-event popup in footer (skip on admin and login)
add_action('wp_footer', function() {
    if (is_admin() || wp_doing_ajax() || is_user_logged_in()) return;
    $upcoming = get_posts(array(
        'post_type' => 'event',
        'posts_per_page' => 1,
        'meta_key' => 'event_date',
        'orderby' => 'meta_value',
        'order' => 'ASC',
        'meta_query' => array(
            array(
                'key' => 'event_date',
                'value' => date('Y-m-d'),
                'compare' => '>=',
                'type' => 'DATE'
            )
        )
    ));
    if ($upcoming) {
        $e = $upcoming[0];

        $date = get_field('event_date', $e->ID);
        $loc = get_field('event_location', $e->ID);
        $permalink = get_permalink($e->ID);
        echo '<div class="gift631-next-event" style="position:fixed;right:16px;bottom:16px;z-index:9999;background:#000;color:#fff;padding:12px 16px;border-radius:8px;box-shadow:0 8px 24px rgba(0,0,0,.3)">';
        echo '<a href="'.esc_url($permalink).'" style="color:#fff;text-decoration:none">';
        echo '<strong>Next event:</strong> '.esc_html(get_the_title($e)).' — '.esc_html($date).($loc?' @ '.esc_html($loc):'');
        echo '</a>';
        echo '</div>';
    }
});


// Restrict single pages for partners and conditionally for services
add_action('template_redirect', function(){
    if (is_singular('partner')) {
        // Always redirect partner singles to archive
        wp_redirect(get_post_type_archive_link('partner'), 302);
        exit;
    }
    if (is_singular('service')) {
        // Only allow detail if toggle is enabled
        $show = get_field('show_detail_page', get_queried_object_id());
        if (!$show) {
            wp_redirect(get_post_type_archive_link('service'), 302);
            exit;
        }
    }
});

// Global block settings: background color + margin toggles (exclude some blocks)
add_action('acf/init', function(){
    if (!function_exists('acf_add_local_field_group')) return;
    $blocks = array(
        'gift-services','gift-title-text','gift-sticky-big-media','gift-big-text-marquee','gift-event',
        'gift-media-slider','gift-information','gift-media-text','gift-latest-news-slider','gift-testimonials',
        'gift-intro-text','gift-text-grid','gift-timeline','gift-credentials','gift-approach','gift-usps',
        'gift-media-grid','gift-ctas','gift-title-text-list','gift-upcoming-events','gift-media-text-small',
        'gift-testimonial-highlight','gift-event-information','gift-privacy-statement','gift-generic'
    ); // excluded: gift-big-header, gift-partners-marquee, gift-signup-block

    $locations = array();
    foreach($blocks as $slug){
        $locations[] = array(array('param'=>'block','operator'=>'==','value'=>"acf/$slug"));
    }

    acf_add_local_field_group(array(
        'key' => 'group_gift631_block_settings',
        'title' => 'Block Settings',
        'fields' => array(
            array(
                'key' => 'field_gift631_block_bg',
                'label' => 'Background',
                'name' => 'background_color',
                'type' => 'select',
                'choices' => array('grey'=>'Grey','white'=>'White', 'dark'=>'Dark'),
                'default_value' => 'grey',
                'return_format' => 'value',
                'ui' => 1,
            ),
            array(
                'key' => 'field_gift631_block_no_margin_top',
                'label' => 'No margin top',
                'name' => 'no_margin_top',
                'type' => 'true_false',
                'ui' => 1,
                'default_value' => 0,
            ),
            array(
                'key' => 'field_gift631_block_no_margin_bottom',
                'label' => 'No margin bottom',
                'name' => 'no_margin_bottom',
                'type' => 'true_false',
                'ui' => 1,
                'default_value' => 0,
            ),
        ),
        'location' => $locations,
    ));
});

// Helper to compose block classes from ACF settings
function gift631_get_block_classes($extra = ''){
    $classes = array();
    $bg = get_field('background_color');
    if (!$bg) { $bg = 'grey'; }
    $classes[] = $bg === 'white' ? 'white' : 'grey' . ($bg === 'dark' ? ' dark' : '');
    if (get_field('no_margin_top')) { $classes[] = 'noMarginTop'; }
    if (get_field('no_margin_bottom')) { $classes[] = 'noMarginBottom'; }
    if ($extra) { $classes[] = trim($extra); }
    return trim(implode(' ', array_filter($classes)));
}

